#include "monitorcard.h"
#include <QPainter>
#include <QVBoxLayout>
#include <QEnterEvent>
#include <QMenu>
#include <QAction>
#include <QColorDialog>
#include <QContextMenuEvent>

// 默认颜色策略实现
QColor MonitorCard::defaultColorStrategy(int value)
{
    if (value < 50) {
        return Qt::green;
    } else if (value < 80) {
        return QColor(255, 165, 0);  // 橙色
    } else {
        return Qt::red;
    }
}

// 修改构造函数
MonitorCard::MonitorCard(
    const QString& label,
    const ColorStrategy& colorStrategy,
    DataType dataType,
    QWidget *parent
) : QFrame(parent)
    , m_label(label)
    , m_value(0)
    , m_dataType(dataType)
    , m_colorStrategy(colorStrategy)
    , m_backgroundStyle(Default)
    , m_customBackgroundColor(Qt::white)
    , m_hovered(false)
    , m_contextMenu(nullptr)
    , m_deleteAction(nullptr)
    , m_backgroundDefaultAction(nullptr)
    , m_backgroundGlassAction(nullptr)
    , m_backgroundGreenAction(nullptr)
    , m_backgroundOrangeAction(nullptr)
    , m_backgroundBlueAction(nullptr)
    , m_backgroundCustomAction(nullptr)
{
    // 设置默认卡片样式
    updateStyle();
    setCursor(Qt::PointingHandCursor);
    setFixedSize(80, 100);
    
    // 创建布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    mainLayout->setSpacing(5);
    mainLayout->setAlignment(Qt::AlignCenter);
    
    // 创建图标标签
    m_iconLabel = new QLabel();
    m_iconLabel->setAlignment(Qt::AlignCenter);
    m_iconLabel->setPixmap(drawCircularProgressBar(m_value, 40));
    
    // 创建数值标签
    m_valueLabel = new QLabel(QString::number(m_value) + "%");
    m_valueLabel->setAlignment(Qt::AlignCenter);
    m_valueLabel->setStyleSheet("font-weight: bold; font-size: 12px;");
    
    // 创建名称标签
    m_nameLabel = new QLabel(m_label);
    m_nameLabel->setAlignment(Qt::AlignCenter);
    m_nameLabel->setStyleSheet("font-size: 9px; color: #5f6368;");
    
    mainLayout->addWidget(m_iconLabel);
    mainLayout->addWidget(m_valueLabel);
    mainLayout->addWidget(m_nameLabel);
    
    // 创建右键菜单
    createContextMenu();
}

// 修改 setValue 函数
void MonitorCard::setValue(int value, const QString& unit)
{
    m_value = value;
    
    // 根据数据类型格式化显示
    QString valueText;
    switch (m_dataType) {
    case Memory:
        if (value >= 1024) {
            valueText = QString::number(value / 1024.0, 'f', 1) + "GB";
        } else {
            valueText = QString::number(value) + "MB";
        }
        break;
    case Disk:
        if (value >= 1024) {
            valueText = QString::number(value / 1024.0, 'f', 1) + "TB";
        } else {
            valueText = QString::number(value) + "GB";
        }
        break;
    case Network:
        if (value >= 1024) {
            valueText = QString::number(value / 1024.0, 'f', 1) + "MB/s";
        } else {
            valueText = QString::number(value) + "KB/s";
        }
        break;
    default: // Percentage
        valueText = QString::number(value) + "%";
        break;
    }
    
    m_valueLabel->setText(valueText);
    m_iconLabel->setPixmap(drawCircularProgressBar(m_value, 40));
}

// 添加设置数据类型的方法
void MonitorCard::setDataType(DataType type)
{
    m_dataType = type;
}

void MonitorCard::setLabel(const QString &label)
{
    m_label = label;
    m_nameLabel->setText(m_label);
}

void MonitorCard::setBackgroundStyle(BackgroundStyle style)
{
    m_backgroundStyle = style;
    updateStyle();
}

void MonitorCard::setCustomBackgroundColor(const QColor& color)
{
    m_customBackgroundColor = color;
    m_backgroundStyle = CustomColor;
    updateStyle();
}

void MonitorCard::setColorStrategy(const ColorStrategy& strategy)
{
    m_colorStrategy = strategy;
}

void MonitorCard::enterEvent(QEnterEvent *event)
{
    m_hovered = true;
    updateStyle();
    QFrame::enterEvent(event);
}

void MonitorCard::leaveEvent(QEvent *event)
{
    m_hovered = false;
    updateStyle();
    QFrame::leaveEvent(event);
}

void MonitorCard::contextMenuEvent(QContextMenuEvent *event)
{
    if (m_contextMenu) {
        m_contextMenu->exec(event->globalPos());
    }
    QFrame::contextMenuEvent(event);
}

void MonitorCard::onBackgroundStyleChanged()
{
    // 更新背景样式菜单项的选中状态
    m_backgroundDefaultAction->setChecked(m_backgroundStyle == Default);
    m_backgroundGlassAction->setChecked(m_backgroundStyle == Glass);
    m_backgroundGreenAction->setChecked(m_backgroundStyle == Green);
    m_backgroundOrangeAction->setChecked(m_backgroundStyle == Orange);
    m_backgroundBlueAction->setChecked(m_backgroundStyle == Blue);
}


void MonitorCard::updateStyle()
{
    QString styleSheet;
    
    switch (m_backgroundStyle) {
    case Default:
        if (m_hovered) {
            styleSheet = "MonitorCard { "
                         "  background-color: #f8f9fa; "
                         "  border: 1px solid #4285f4; "
                         "  border-radius: 8px; "
                         "}";
        } else {
            styleSheet = "MonitorCard { "
                         "  background-color: #ffffff; "
                         "  border: 1px solid #e0e0e0; "
                         "  border-radius: 8px; "
                         "}"
                         "MonitorCard:hover { "
                         "  border: 1px solid #4285f4; "
                         "  background-color: #f8f9fa; "
                         "}";
        }
        break;
        
    case Glass:
        if (m_hovered) {
            styleSheet = "MonitorCard { "
                         "  background-color: rgba(255, 255, 255, 0.9); "
                         "  border: 1px solid #4285f4; "
                         "  border-radius: 8px; "
                         "}";
        } else {
            styleSheet = "MonitorCard { "
                         "  background-color: rgba(255, 255, 255, 0.7); "
                         "  border: 1px solid rgba(200, 200, 200, 0.5); "
                         "  border-radius: 8px; "
                         "}"
                         "MonitorCard:hover { "
                         "  border: 1px solid #4285f4; "
                         "  background-color: rgba(255, 255, 255, 0.9); "
                         "}";
        }
        break;
        
    case Green:
        if (m_hovered) {
            styleSheet = "MonitorCard { "
                         "  background-color: #c8e6c9; "
                         "  border: 1px solid #4285f4; "
                         "  border-radius: 8px; "
                         "}";
        } else {
            styleSheet = "MonitorCard { "
                         "  background-color: #e8f5e9; "
                         "  border: 1px solid #c8e6c9; "
                         "  border-radius: 8px; "
                         "}"
                         "MonitorCard:hover { "
                         "  border: 1px solid #4285f4; "
                         "  background-color: #c8e6c9; "
                         "}";
        }
        break;
        
    case Orange:
        if (m_hovered) {
            styleSheet = "MonitorCard { "
                         "  background-color: #ffe0b2; "
                         "  border: 1px solid #4285f4; "
                         "  border-radius: 8px; "
                         "}";
        } else {
            styleSheet = "MonitorCard { "
                         "  background-color: #fff3e0; "
                         "  border: 1px solid #ffe0b2; "
                         "  border-radius: 8px; "
                         "}"
                         "MonitorCard:hover { "
                         "  border: 1px solid #4285f4; "
                         "  background-color: #ffe0b2; "
                         "}";
        }
        break;
        
    case Blue:
        if (m_hovered) {
            styleSheet = "MonitorCard { "
                         "  background-color: #bbdefb; "
                         "  border: 1px solid #4285f4; "
                         "  border-radius: 8px; "
                         "}";
        } else {
            styleSheet = "MonitorCard { "
                         "  background-color: #e3f2fd; "
                         "  border: 1px solid #bbdefb; "
                         "  border-radius: 8px; "
                         "}"
                         "MonitorCard:hover { "
                         "  border: 1px solid #4285f4; "
                         "  background-color: #bbdefb; "
                         "}";
        }
        break;
        
    case CustomColor:
        if (m_hovered) {
            styleSheet = QString("MonitorCard { "
                                "  background-color: %1; "
                                "  border: 1px solid #4285f4; "
                                "  border-radius: 8px; "
                                "}").arg(m_customBackgroundColor.lighter(110).name());
        } else {
            styleSheet = QString("MonitorCard { "
                                "  background-color: %1; "
                                "  border: 1px solid %2; "
                                "  border-radius: 8px; "
                                "}"
                                "MonitorCard:hover { "
                                "  border: 1px solid #4285f4; "
                                "  background-color: %3; "
                                "}")
                                .arg(m_customBackgroundColor.name())
                                .arg(m_customBackgroundColor.darker(120).name())
                                .arg(m_customBackgroundColor.lighter(110).name());
        }
        break;
    }
    
    setStyleSheet(styleSheet);
}

QPixmap MonitorCard::drawCircularProgressBar(int value, int size)
{
    QPixmap pixmap(size, size);
    pixmap.fill(Qt::transparent);
    
    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);
    
    QRect rect(0, 0, size, size);
    
    // 绘制背景圆
    painter.setPen(QPen(QColor(220, 220, 220), 4));
    painter.drawEllipse(rect.adjusted(2, 2, -2, -2));
    
    // 根据值选择颜色
    QColor color = getColorForValue(value);
    
    // 绘制进度圆弧
    if (value > 0) {
        painter.setPen(QPen(color, 4));
        int angle = (value * 360) / 100;
        painter.drawArc(rect.adjusted(2, 2, -2, -2), 90 * 16, -angle * 16);
    }
    
    return pixmap;
}

QColor MonitorCard::getColorForValue(int value)
{
    return m_colorStrategy(value);
}

void MonitorCard::createContextMenu()
{
    m_contextMenu = new QMenu(this);
    
    // 删除操作
    m_deleteAction = new QAction("删除", this);
    connect(m_deleteAction, &QAction::triggered, this, &MonitorCard::onDeleteActionTriggered);
    m_contextMenu->addAction(m_deleteAction);
    
    m_contextMenu->addSeparator();
    
    // 背景样式选项
    m_backgroundDefaultAction = new QAction("默认背景", this);
    m_backgroundDefaultAction->setCheckable(true);
    connect(m_backgroundDefaultAction, &QAction::triggered, this, [this]() {
        setBackgroundStyle(Default);
    });
    
    m_backgroundGlassAction = new QAction("玻璃效果", this);
    m_backgroundGlassAction->setCheckable(true);
    connect(m_backgroundGlassAction, &QAction::triggered, this, [this]() {
        setBackgroundStyle(Glass);
    });
    
    m_backgroundGreenAction = new QAction("绿色背景", this);
    m_backgroundGreenAction->setCheckable(true);
    connect(m_backgroundGreenAction, &QAction::triggered, this, [this]() {
        setBackgroundStyle(Green);
    });
    
    m_backgroundOrangeAction = new QAction("橙色背景", this);
    m_backgroundOrangeAction->setCheckable(true);
    connect(m_backgroundOrangeAction, &QAction::triggered, this, [this]() {
        setBackgroundStyle(Orange);
    });
    
    m_backgroundBlueAction = new QAction("蓝色背景", this);
    m_backgroundBlueAction->setCheckable(true);
    connect(m_backgroundBlueAction, &QAction::triggered, this, [this]() {
        setBackgroundStyle(Blue);
    });
    
    m_backgroundCustomAction = new QAction("自定义颜色...", this);
    connect(m_backgroundCustomAction, &QAction::triggered, this, [this]() {
        QColor color = QColorDialog::getColor(m_customBackgroundColor, this, "选择背景颜色");
        if (color.isValid()) {
            setCustomBackgroundColor(color);
        }
    });
    
    m_contextMenu->addAction(m_backgroundDefaultAction);
    m_contextMenu->addAction(m_backgroundGlassAction);
    m_contextMenu->addAction(m_backgroundGreenAction);
    m_contextMenu->addAction(m_backgroundOrangeAction);
    m_contextMenu->addAction(m_backgroundBlueAction);
    m_contextMenu->addAction(m_backgroundCustomAction);
    
    // 更新菜单项的选中状态
    connect(m_contextMenu, &QMenu::aboutToShow, this, &MonitorCard::onBackgroundStyleChanged);
}

// 添加删除操作的实现
void MonitorCard::onDeleteActionTriggered()
{
    emit deleted(m_label);
    // 从父布局中移除自己
    if (parentWidget() && parentWidget()->layout()) {
        QHBoxLayout *layout = qobject_cast<QHBoxLayout*>(parentWidget()->layout());
        if (layout) {
            layout->removeWidget(this);
        }
    }
    
    // 删除自己
    deleteLater();
}
