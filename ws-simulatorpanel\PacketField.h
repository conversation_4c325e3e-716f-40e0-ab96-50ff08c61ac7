// PacketField.h
#ifndef PACKETFIELD_H
#define PACKETFIELD_H

#include <QString>
#include <QVariant>

class PacketField
{
public:
    enum Type {
        BOOL,
        BYTE,
        INT,
        DINT,
        WORD,
        DWORD,
        REAL,
        STRING
    };

    PacketField(Type type = INT, const QString& name = QString(), const QVariant& value = QVariant(), int stringLength = 10)
        : m_type(type), m_name(name), m_value(value), m_stringLength(stringLength) {}
    
    Type type() const { return m_type; }
    void setType(Type type) { m_type = type; }
    
    QString name() const { return m_name; }
    void setName(const QString& name) { m_name = name; }
    
    QVariant value() const { return m_value; }
    void setValue(const QVariant& value) { m_value = value; }

    int stringLength() const { return m_stringLength; }
    void setStringLength(int length) { m_stringLength = length; }
    
    int size() const {
        switch (m_type) {
        case BOOL:
        case BYTE: return 1;
        case INT:
        case WORD: return 2;
        case DINT:
        case DWORD:
        case REAL: return 4;
        case STRING: return m_stringLength;
        }
        return 0;
    }
    
    static QString typeToString(Type type) {
        switch (type) {
        case BOOL: return "BOOL";
        case BYTE: return "BYTE";
        case INT: return "INT";
        case DINT: return "DINT";
        case WORD: return "WORD";
        case DWORD: return "DWORD";
        case REAL: return "REAL";
        case STRING: return "STRING";
            break;
        }
        return "INT";
    }
    
    static Type stringToType(const QString& str) {
        if (str == "BOOL") return BOOL;
        if (str == "BYTE") return BYTE;
        if (str == "INT") return INT;
        if (str == "DINT") return DINT;
        if (str == "WORD") return WORD;
        if (str == "DWORD") return DWORD;
        if (str == "REAL") return REAL;
        if (str == "STRING") return STRING;
        return INT;
    }

    // 获取补全后的字符串值（用于STRING类型）
    QString getPaddedStringValue() const {
        if (m_type != STRING) {
            return m_value.toString();
        }

        QString str = m_value.toString();
        if (str.length() >= m_stringLength) {
            return str.left(m_stringLength); // 截断超长字符串
        } else {
            return str.leftJustified(m_stringLength, '\0'); // 用空字符补全
        }
    }

private:
    Type m_type;
    QString m_name;
    QVariant m_value;
    int m_stringLength;
};

#endif // PACKETFIELD_H
