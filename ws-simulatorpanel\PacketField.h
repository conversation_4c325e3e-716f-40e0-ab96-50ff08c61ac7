// PacketField.h
#ifndef PACKETFIELD_H
#define PACKETFIELD_H

#include <QString>
#include <QVariant>

class PacketField
{
public:
    enum Type {
        BOOL,
        BYTE,
        INT,
        DINT,
        WORD,
        DWORD,
        REAL
    };
    
    PacketField(Type type = INT, const QString& name = QString(), const QVariant& value = QVariant())
        : m_type(type), m_name(name), m_value(value) {}
    
    Type type() const { return m_type; }
    void setType(Type type) { m_type = type; }
    
    QString name() const { return m_name; }
    void setName(const QString& name) { m_name = name; }
    
    QVariant value() const { return m_value; }
    void setValue(const QVariant& value) { m_value = value; }
    
    int size() const {
        switch (m_type) {
        case BOOL:
        case BYTE: return 1;
        case INT:
        case WORD: return 2;
        case DINT:
        case DWORD:
        case REAL: return 4;
        }
        return 0;
    }
    
    static QString typeToString(Type type) {
        switch (type) {
        case BOOL: return "BOOL";
        case BYTE: return "BYTE";
        case INT: return "INT";
        case DINT: return "DINT";
        case WORD: return "WORD";
        case DWORD: return "DWORD";
        case REAL: return "REAL";
            break;
        }
        return "INT";
    }
    
    static Type stringToType(const QString& str) {
        if (str == "BOOL") return BOOL;
        if (str == "BYTE") return BYTE;
        if (str == "INT") return INT;
        if (str == "DINT") return DINT;
        if (str == "WORD") return WORD;
        if (str == "DWORD") return DWORD;
        if (str == "REAL") return REAL;
        return INT;
    }

private:
    Type m_type;
    QString m_name;
    QVariant m_value;
};

#endif // PACKETFIELD_H
