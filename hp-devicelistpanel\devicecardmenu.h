// devicecardmenu.h
#ifndef DEVICECARDMENU_H
#define DEVICECARDMENU_H

#include <QMenu>
#include <QAction>

class DeviceCardMenu : public QMenu
{
    Q_OBJECT

public:
    explicit DeviceCardMenu(QWidget *parent = nullptr);
    
    QAction* deleteAction() const { return m_deleteAction; }
    QAction* compareAction() const { return m_compareAction; }
    QAction* changeIconAction() const { return m_changeIconAction; }
    QAction* openConfigDirAction() const { return m_openConfigDirAction; }
    QAction* toggleFavoriteAction() const { return m_toggleFavoriteAction; }

private:
    void setupStyles();
    
    QAction *m_deleteAction;
    QAction *m_compareAction;
    QAction *m_changeIconAction;
    QAction *m_openConfigDirAction;
    QAction *m_toggleFavoriteAction;
};

#endif // DEVICECARDMENU_H