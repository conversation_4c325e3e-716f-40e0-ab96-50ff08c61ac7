// DeviceDetailsPanel.cpp
#include "DeviceDetailsPanel.h"
#include "ui_devicedetailspanel.h"
#include "homepage/mainwindow.h"
#include <QLabel>
#include <QVBoxLayout>
#include <QFormLayout>
#include <QPixmap>
#include <QPainter>
#include <QPainterPath>

DeviceDetailsPanel::DeviceDetailsPanel(QWidget *parent) :
    QFrame(parent),
    ui(new Ui::DeviceDetailsPanel)
{
    ui->setupUi(this);
    
    // 设置整体样式
    this->setStyleSheet(
        "DeviceDetailsPanel {"
        "  background-color: #ffffff;"
        "  border: 1px solid #e0e0e0;"
        "  border-radius: 8px;"
        "}"
    );
    
    setupStyles();
    clearDeviceDetails();
}

DeviceDetailsPanel::~DeviceDetailsPanel()
{
    delete ui;
}

void DeviceDetailsPanel::setupStyles()
{
    // 设置标题样式
    ui->titleLabel->setStyleSheet(
        "QLabel {"
        "  color: #202124;"
        "  font-weight: 600;"
        "  font-size: 16px;"
        "  margin-bottom: 10px;"
        "}"
    );
    
    // 设置内容区域样式
    ui->contentLabel->setStyleSheet(
        "QLabel {"
        "  color: #5f6368;"
        "  font-size: 13px;"
        "  line-height: 1.5;"
        "}"
    );
}

void DeviceDetailsPanel::showDeviceDetails(const DeviceInfo* device)
{
    if (device) {
        m_currentDeviceIP = device->ip;
        
        // 设置设备图标首字母
        QString firstLetter = device->name.isEmpty() ? "D" : device->name.left(1).toUpper();
        ui->iconLabel->setText(firstLetter);
        
        // 更新标题
        ui->titleLabel->setText(device->name);
        
        // 根据操作系统设置图标颜色
        QString iconColor;
        if (device->os.contains("Windows", Qt::CaseInsensitive)) {
            iconColor = "#0078d7";  // Windows蓝色
        } else if (device->os.contains("Linux", Qt::CaseInsensitive)) {
            iconColor = "#fcc624";  // Linux黄色
        } else if (device->os.contains("Mac", Qt::CaseInsensitive) || 
                   device->os.contains("Darwin", Qt::CaseInsensitive)) {
            iconColor = "#aaaaaa";  // macOS灰色
        } else {
            iconColor = "#4285f4";  // 默认蓝色
        }
        
        ui->iconLabel->setStyleSheet(QString(
            "QLabel {"
            "  background-color: %1;"
            "  border-radius: 32px;"
            "  color: white;"
            "  font-size: 24px;"
            "  font-weight: bold;"
            "  qproperty-alignment: AlignCenter;"
            "}").arg(iconColor));
        
        // 构建详细信息文本
        QString statusText = device->isOnline ? "在线" : "离线";
        QString favoriteText = device->isFavorite ? "是" : "否";
        
        QString details = QString("<html>"
                                  "<head/>"
                                  "<body>"
                                  "<div style='margin-top: 10px;'>"
                                  "<table cellspacing='8' cellpadding='0' style='width: 100%;'>"
                                  "<tr><td style='color: #888888; font-weight: normal;'>设备名称:</td>"
                                  "<td style='font-weight: 500;'>%1</td></tr>"
                                  "<tr><td style='color: #888888; font-weight: normal;'>IP地址:</td>"
                                  "<td style='font-weight: 500;'>%2</td></tr>"
                                  "<tr><td style='color: #888888; font-weight: normal;'>端口:</td>"
                                  "<td style='font-weight: 500;'>%3</td></tr>"
                                  "<tr><td style='color: #888888; font-weight: normal;'>操作系统:</td>"
                                  "<td style='font-weight: 500;'>%4</td></tr>"
                                  "<tr><td style='color: #888888; font-weight: normal;'>系统版本:</td>"
                                  "<td style='font-weight: 500;'>%5</td></tr>"
                                  "<tr><td style='color: #888888; font-weight: normal;'>状态:</td>"
                                  "<td><span style='color: %6; font-weight: 600;'>%7</span></td></tr>"
                                  "<tr><td style='color: #888888; font-weight: normal;'>收藏:</td>"
                                  "<td style='font-weight: 500;'>%8</td></tr>"
                                  "</table>"
                                  "</div>"
                                  "</body>"
                                  "</html>")
                          .arg(device->name)
                          .arg(device->ip)
                          .arg(device->port)
                          .arg(device->os)
                          .arg(device->osVersion)
                          .arg(device->isOnline ? "#28a745" : "#dc3545")  // 绿色表示在线，红色表示离线
                          .arg(statusText)
                          .arg(favoriteText);
        
        ui->contentLabel->setText(details);
        ui->contentLabel->setAlignment(Qt::AlignLeft | Qt::AlignTop);
    }
}

void DeviceDetailsPanel::clearDeviceDetails()
{
    m_currentDeviceIP.clear();
    ui->titleLabel->setText("设备详情");
    
    QString placeholderText = QString("<html>"
                                      "<head/>"
                                      "<body>"
                                      "<div style='text-align: center; margin: 20px;'>"
                                      "<h3 style='color: #5f6368; margin-bottom: 15px;'>请选择设备查看详细信息</h3>"
                                      "<p style='color: #888888; font-size: 12px; line-height: 1.6;'>"
                                      "• 单击设备卡片查看基本信息<br/>"
                                      "• 双击设备卡片打开工作台<br/>"
                                      "• 右键设备卡片可进行更多操作"
                                      "</p>"
                                      "</div>"
                                      "</body>"
                                      "</html>");
    
    ui->contentLabel->setText(placeholderText);
    ui->contentLabel->setAlignment(Qt::AlignCenter);
}