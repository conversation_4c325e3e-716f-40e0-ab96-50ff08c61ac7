#ifndef DEVICELISTPANEL_H
#define DEVICELISTPANEL_H

#include <QWidget>
#include <QMap>
#include "DeviceCard.h"
#include "utils/deviceinfo.h"

namespace Ui {
class DeviceListPanel;
}

class DeviceListPanel : public QWidget
{
    Q_OBJECT

public:
    explicit DeviceListPanel(QWidget *parent = nullptr);
    ~DeviceListPanel();

    void addDevice(const QString& deviceKey, DeviceInfo* device);
    void removeDevice(const QString& deviceKey);
    void updateDeviceStatus(const QString& deviceKey, bool isOnline);
    void updateDeviceFavoriteStatus(const QString& deviceKey, bool isFavorite);
    void filterDevices(const QString &searchText);
    QList<DeviceCard*> getAllDeviceCards() const;

signals:
    void deviceCardClicked(const QString& deviceKey);
    void deviceCardDoubleClicked(const QString& deviceKey);
    void deviceDeleteRequested(const QString& deviceIP);
    void emptyAreaClicked();
protected:
    void mousePressEvent(QMouseEvent *event)override;
private slots:
    void onDeviceCardClicked();
    void onDeviceCardDoubleClicked();
    void onDeleteDeviceRequested(const QString& deviceIP);

private:
    Ui::DeviceListPanel *ui;
    QMap<QString, DeviceCard*> m_deviceCards;
};

#endif // DEVICELISTPANEL_H
