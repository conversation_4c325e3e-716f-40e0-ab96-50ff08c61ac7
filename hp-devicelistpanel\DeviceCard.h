#ifndef DEVICECARD_H
#define DEVICECARD_H

#include <QFrame>
#include <QLabel>
#include <QDateTime>
#include <QMenu>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include "devicecardmenu.h"

class DeviceCard : public QFrame
{
    Q_OBJECT

public:
    explicit DeviceCard(const QString& deviceName, const QString& deviceIP, bool isFavorite = false, QWidget *parent = nullptr);
    QString deviceName() const { return m_deviceName; }
    QString deviceIP() const { return m_deviceIP; }
    bool isOnline() const { return m_isOnline; }
    void setOnline(bool isOnline);
    void setFavorite(bool isFavorite);

signals:
    void clicked();
    void doubleClicked();
    void deleteDevice(const QString& deviceIP);

protected:
    void mousePressEvent(QMouseEvent *event) override;
    void mouseDoubleClickEvent(QMouseEvent *event) override;
    void contextMenuEvent(QContextMenuEvent *event) override;
    bool eventFilter(QObject *obj, QEvent *event) override;

private slots:
    void updateStatusIndicator();
    void setupUI();
    void onDeleteDevice();
    void onCompareDevice();
    void onChangeIcon();
    void onOpenConfigDir();
    void onToggleFavorite();
    void setDeviceName(const QString& name, bool isFavorite = false);
private:
    QString m_deviceName;
    QString m_deviceIP;
    bool m_isOnline;
    qint64 m_lastClickTime;
    
    QLabel* m_iconLabel;
    QLabel* m_nameLabel;
    QLabel* m_ipLabel;
    QLabel* m_statusIndicator;
    QLabel* m_favoriteLabel;
    
    // 右键菜单相关
    DeviceCardMenu* m_contextMenu; // 更换为自定义菜单类
    bool m_isFavorite;
};

#endif // DEVICECARD_H