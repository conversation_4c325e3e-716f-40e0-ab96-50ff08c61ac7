#ifndef SIMULATORCONFIGDIALOG_H
#define SIMULATORCONFIGDIALOG_H

#include <QDialog>
#include <QMap>
#include <QString>
#include <QVector>
#include <QWidget>
#include <QLineEdit>
#include <QSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QTableWidget>
#include <QLabel>
#include <QDialogButtonBox>

class Simulator;
class SimulatorConfigDialog : public QDialog
{
    Q_OBJECT

public:
    explicit SimulatorConfigDialog(Simulator *simulator, QWidget *parent = nullptr);
    
private slots:
    void saveSettings();
    void addField();
    void removeField();
    void batchAddFields();
    void updatePacketInfo();

private:
    void setupUI();
    void loadSettings();
    void updateFieldWidgets();
    void setupFieldTypeComboBox(int row, int column);
    
    Simulator *m_simulator;
    QMap<int, QComboBox*> m_typeComboBoxes;
    QLineEdit *m_nameEdit;
    QLineEdit *m_targetAddressEdit;
    QSpinBox *m_targetPortSpinBox;
    QSpinBox *m_messageIntervalSpinBox;
    
    // 报文配置控件
    QSpinBox *m_packetIdSpinBox;
    QCheckBox *m_bigEndianCheckBox;
    QPushButton *m_addFieldButton;
    QPushButton *m_removeFieldButton;
    QPushButton *m_batchAddButton;
    QTableWidget *m_fieldsTable;
    QLabel *m_packetInfoLabel;
    
    QDialogButtonBox *m_buttonBox;
};

#endif
