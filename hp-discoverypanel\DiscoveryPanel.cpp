#include "DiscoveryPanel.h"
#include "ui_discoverypanel.h"
#include "DiscoveredDeviceCard.h"
#include "utils/deviceinfo.h"
#include <QStyle>
#include <QScrollBar>

DiscoveryPanel::DiscoveryPanel(DiscoveredDevicesManager *discoveryManager, QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::DiscoveryPanel)
    , m_discoveryManager(discoveryManager)
    , m_devicesMap(nullptr)
    , m_isExpanded(true)
{
    ui->setupUi(this);
    
    // 设置初始图标
    ui->toggleButton->setIcon(style()->standardIcon(QStyle::SP_ArrowDown));
    
    // 连接信号槽
    connect(ui->toggleButton, &QPushButton::clicked, this, &DiscoveryPanel::togglePanel);
    
    // 连接发现管理器的信号（修改为接收完整设备信息）
    connect(m_discoveryManager, &DiscoveredDevicesManager::deviceDiscovered,
            this, &DiscoveryPanel::onDeviceDiscovered);
    connect(m_discoveryManager, &DiscoveredDevicesManager::deviceRemoved,
            this, &DiscoveryPanel::onDeviceRemoved);
    
    // 确保滚动区域正确设置
    ui->scrollArea->setWidgetResizable(false);
    ui->scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    ui->scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
}

DiscoveryPanel::~DiscoveryPanel()
{
    delete ui;
}

void DiscoveryPanel::setDevicesMap(const QMap<QString, void*> *devicesMap)
{
    m_devicesMap = devicesMap;
}

void DiscoveryPanel::togglePanel()
{
    m_isExpanded = !m_isExpanded;
    
    if (m_isExpanded) {
        // 展开面板
        ui->dockWidget->setMinimumHeight(130);
        ui->dockWidget->setMaximumHeight(16777215);
        ui->contentWidget->setVisible(true);
        ui->toggleButton->setIcon(style()->standardIcon(QStyle::SP_ArrowDown));
        ui->titleLabel->setText("发现的设备");
        
        // 展开时确保显示所有设备卡片
        ui->resultsContainer->adjustSize();
        ui->resultsLayout->update();
    } else {
        // 收起面板
        ui->dockWidget->setMinimumHeight(30);
        ui->dockWidget->setMaximumHeight(30);
        ui->contentWidget->setVisible(false);
        updatePanelTitle();
        ui->toggleButton->setIcon(style()->standardIcon(QStyle::SP_ArrowUp));
    }
    
    // 立即更新布局
    ui->dockWidget->updateGeometry();
    this->layout()->update();
    
    // 通知父窗口重新布局
    if (parentWidget()) {
        parentWidget()->layout()->update();
    }
}

void DiscoveryPanel::onDeviceDiscovered(const DiscoveredDevice &device)
{
    qDebug() << "Device discovered: " << device.name << " (" << device.ip << ")";
    // 检查设备是否已经添加到监控列表
    bool isAdded = false;
    if (m_devicesMap) {
        // 遍历设备映射，检查是否有匹配的IP地址
        QMap<QString, void*>::const_iterator it = m_devicesMap->constBegin();
        while (it != m_devicesMap->constEnd()) {
            DeviceInfo* deviceInfo = static_cast<DeviceInfo*>(it.value());
            if (deviceInfo && deviceInfo->ip == device.ip) {
                isAdded = true;
                break;
            }
            ++it;
        }
    }
    
    // 检查设备是否已经显示在发现面板中
    bool alreadyDisplayed = false;
    DiscoveredDeviceCard *existingCard = nullptr;
    
    for (int i = 0; i < ui->resultsLayout->count(); ++i) {
        QLayoutItem *item = ui->resultsLayout->itemAt(i);
        if (item && item->widget()) {
            DiscoveredDeviceCard *card = qobject_cast<DiscoveredDeviceCard*>(item->widget());
            if (card && card->deviceIP() == device.ip) {
                alreadyDisplayed = true;
                existingCard = card;
                break;
            }
        }
    }
    
    // 如果尚未显示，则创建新的设备卡片
    if (!alreadyDisplayed) {
        DiscoveredDeviceCard *card = new DiscoveredDeviceCard(device, isAdded);
        connect(card, &DiscoveredDeviceCard::addDeviceClicked, this, 
                [this](const DiscoveredDevice &deviceInfo) {
                    emit addDeviceRequested(deviceInfo);
                });
        ui->resultsLayout->addWidget(card);
        card->show();
    } else if (existingCard) {
        // 如果已经显示但状态发生变化，更新卡片状态
        existingCard->updateAddedStatus(isAdded);
    }
    
    // 更新标题显示计数（仅在面板折叠时显示）
    if (!m_isExpanded) {
        updatePanelTitle();
    }
    
    // 强制更新布局和大小
    ui->resultsContainer->adjustSize();
    ui->resultsLayout->update();
}

void DiscoveryPanel::onDeviceRemoved(const QString &deviceIP)
{
    // 从发现面板中移除设备卡片
    for (int i = 0; i < ui->resultsLayout->count(); ++i) {
        QLayoutItem *item = ui->resultsLayout->itemAt(i);
        if (item && item->widget()) {
            DiscoveredDeviceCard *card = qobject_cast<DiscoveredDeviceCard*>(item->widget());
            if (card && card->deviceIP() == deviceIP) {
                // 从布局中移除并删除卡片
                ui->resultsLayout->removeWidget(card);
                card->deleteLater();
                break;
            }
        }
    }
    
    // 更新标题显示计数（仅在面板折叠时显示）
    if (!m_isExpanded) {
        updatePanelTitle();
    }
    
    // 强制更新布局
    ui->resultsContainer->adjustSize();
    ui->resultsLayout->update();
}

void DiscoveryPanel::updatePanelTitle()
{
    ui->titleLabel->setText(QString("发现的设备 (%1)").arg(ui->resultsLayout->count()));
}

void DiscoveryPanel::updateDeviceAddedStatus(const QString &deviceIP, bool isAdded)
{
    // 遍历发现面板中的所有设备卡片并更新其状态
    for (int i = 0; i < ui->resultsLayout->count(); ++i) {
        QLayoutItem *item = ui->resultsLayout->itemAt(i);
        if (item && item->widget()) {
            DiscoveredDeviceCard *card = qobject_cast<DiscoveredDeviceCard*>(item->widget());
            if (card && card->deviceIP() == deviceIP) {
                card->updateAddedStatus(isAdded);
                break;
            }
        }
    }
}
