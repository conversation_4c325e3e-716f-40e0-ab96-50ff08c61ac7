#include "theme_toggle_button.h"
#include <QStyleOption>
#include <QStylePainter>
#include <QToolTip>
#include <cmath> // 添加这个包含

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif
ThemeToggleButton::ThemeToggleButton(QWidget *parent)
    : QPushButton(parent)
    , m_currentTheme(ThemeManager::Light)
{
    setFixedSize(40, 40);
    setCursor(Qt::PointingHandCursor);
    setFocusPolicy(Qt::NoFocus);
    
    // 连接主题变化信号
    connect(ThemeManager::instance(), &ThemeManager::themeChanged, 
            this, &ThemeToggleButton::onThemeChanged);
    
    // 初始化当前主题
    m_currentTheme = ThemeManager::instance()->currentTheme();
    updateToolTip();
}

void ThemeToggleButton::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // 绘制背景
    painter.setPen(Qt::NoPen);
    
    // 根据当前主题设置按钮背景色
    if (m_currentTheme == ThemeManager::Light) {
        painter.setBrush(QColor(240, 240, 240));
    } else {
        painter.setBrush(QColor(60, 60, 60));
    }
    
    painter.drawRoundedRect(rect(), 6, 6);
    
    // 绘制主题图标
    QRect iconRect = rect().adjusted(8, 8, -8, -8);
    
    if (m_currentTheme == ThemeManager::Light) {
        // 绘制太阳图标（明亮主题）
        painter.setPen(QPen(QColor(255, 193, 7), 2));
        painter.setBrush(Qt::NoBrush);
        
        // 绘制太阳圆盘
        painter.drawEllipse(iconRect.center(), 6, 6);
        
        // 绘制太阳光芒
        painter.setPen(QPen(QColor(255, 193, 7), 2, Qt::SolidLine, Qt::RoundCap));
        for (int i = 0; i < 8; ++i) {
            qreal angle = i * 45 * M_PI / 180;
            QPointF start(
                iconRect.center().x() + 7 * cos(angle),
                iconRect.center().y() + 7 * sin(angle)
            );
            QPointF end(
                iconRect.center().x() + 11 * cos(angle),
                iconRect.center().y() + 11 * sin(angle)
            );
            painter.drawLine(start, end);
        }
    } else {
        // 绘制月亮图标（深色主题）
        painter.setPen(QPen(QColor(92, 107, 192), 2));
        painter.setBrush(Qt::NoBrush);
        
        // 绘制月亮外弧
        QRect moonRect(iconRect.center().x() - 6, iconRect.center().y() - 6, 12, 12);
        painter.drawArc(moonRect, 45 * 16, 180 * 16);
        
        // 绘制月亮内弧，形成月牙效果
        painter.setPen(QPen(QColor(60, 60, 60), 2));
        QRect innerMoonRect(iconRect.center().x() - 3, iconRect.center().y() - 6, 6, 12);
        painter.drawArc(innerMoonRect, 45 * 16, 180 * 16);
    }
}

void ThemeToggleButton::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        // 切换主题
        ThemeManager::Theme newTheme = (m_currentTheme == ThemeManager::Light) ? 
                                       ThemeManager::Dark : ThemeManager::Light;
        ThemeManager::instance()->setTheme(newTheme);
    }
    QPushButton::mousePressEvent(event);
}

void ThemeToggleButton::onThemeChanged(ThemeManager::Theme theme)
{
    m_currentTheme = theme;
    updateToolTip();
    update(); // 重绘按钮
}

void ThemeToggleButton::updateToolTip()
{
    if (m_currentTheme == ThemeManager::Light) {
        setToolTip("切换到深色主题");
    } else {
        setToolTip("切换到明亮主题");
    }
}