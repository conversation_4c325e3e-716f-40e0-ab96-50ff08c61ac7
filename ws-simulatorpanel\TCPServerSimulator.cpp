// TCPServerSimulator.cpp
#include "TCPServerSimulator.h"
#include <QDateTime>
#include <QDebug>

TCPServerSimulator::TCPServerSimulator(QObject *parent)
    : Simulator(parent)
    , m_tcpServer(new QTcpServer(this))
{
    connect(m_timer, &QTimer::timeout, this, &TCPServerSimulator::sendTCPMessage);
    connect(m_tcpServer, &QTcpServer::newConnection, this, &TCPServerSimulator::onNewConnection);
#if QT_VERSION >= QT_VERSION_CHECK(6, 0, 0)
    connect(m_tcpServer, &QTcpServer::acceptError, this, &TCPServerSimulator::onError);
#else
    connect(m_tcpServer, QOverload<QAbstractSocket::SocketError>::of(&QTcpServer::serverError),
            this, &TCPServerSimulator::onError);
#endif
}

TCPServerSimulator::~TCPServerSimulator()
{
    stop();
}

void TCPServerSimulator::start()
{
    if (m_tcpServer->listen(QHostAddress::Any, m_targetPort)) {
        m_timer->start(m_messageInterval);
        m_isRunning = true;
        emit messageSent(QString("%1 已启动，监听端口 %2")
                        .arg(m_name)
                        .arg(m_targetPort));
    } else {
        emit errorOccurred(QString("%1 启动失败: %2")
                          .arg(m_name)
                          .arg(m_tcpServer->errorString()));
    }
}

void TCPServerSimulator::stop()
{
    m_timer->stop();
    
    // 断开所有客户端连接
    for (QTcpSocket *client : m_clients) {
        client->disconnectFromHost();
    }
    m_clients.clear();
    
    // 停止服务器监听
    if (m_tcpServer->isListening()) {
        m_tcpServer->close();
    }
    
    m_isRunning = false;
    emit messageSent(QString("%1 已停止").arg(m_name));
}


QByteArray TCPServerSimulator::buildPacket()
{
    QByteArray packet;
    QDataStream stream(&packet, QIODevice::WriteOnly);
    
    // 设置字节序
    stream.setByteOrder(m_bigEndian ? QDataStream::BigEndian : QDataStream::LittleEndian);
    
    // 计算数据区大小
    int dataSize = 0;
    for (const PacketField& field : m_packetFields) {
        dataSize += field.size();
    }
    
    // TCP报文头格式: 报文长度(2字节) + 报文ID(2字节) + 计数器(4字节) + 数据区
    quint16 packetLength = 8 + dataSize; // 报文头8字节 + 数据区大小
    
    // 添加TCP报文头 (报文长度 + 报文ID + 计数器)
    stream << packetLength;  // 报文长度(2字节)
    stream << m_packetId;    // 报文ID(2字节)
    stream << m_packageCnt;  // 计数器(4字节)
    
    m_packageCnt++;
    
    // 添加数据区
    for (const PacketField& field : m_packetFields) {
        switch (field.type()) {
        case PacketField::BOOL:
            stream << static_cast<quint8>(field.value().toUInt());
            break;
        case PacketField::BYTE:
            stream << static_cast<quint8>(field.value().toUInt());
            break;
        case PacketField::INT:
            stream << static_cast<qint16>(field.value().toInt());
            break;
        case PacketField::DINT:
            stream << static_cast<qint32>(field.value().toInt());
            break;
        case PacketField::WORD:
            stream << static_cast<quint16>(field.value().toUInt());
            break;
        case PacketField::DWORD:
            stream << static_cast<quint32>(field.value().toUInt());
            break;
        case PacketField::REAL:
            float value = field.value().toFloat();
            stream.writeRawData(reinterpret_cast<const char*>(&value), 4);
            break;
        case PacketField::STRING: {
            QString paddedString = field.getPaddedStringValue();
            QByteArray stringBytes = paddedString.toUtf8();
            // 确保写入指定长度的字节
            if (stringBytes.size() < field.stringLength()) {
                stringBytes.resize(field.stringLength());
            }
            stream.writeRawData(stringBytes.constData(), field.stringLength());
            break;
        }
        }
    }
    
    return packet;
}

Simulator::Type TCPServerSimulator::type() const
{
    return TCPServer;
}

void TCPServerSimulator::sendTCPMessage()
{
    if (m_clients.isEmpty()) {
        return;
    }
    
    // 使用与 TCPSimulator 相同的报文构建方法
    QByteArray packet = buildPacket();
    
    // 向所有连接的客户端发送数据，并检查发送状态
    QList<QTcpSocket*> clientsToRemove;
    for (QTcpSocket *client : m_clients) {
        if (client->state() == QAbstractSocket::ConnectedState) {
            qint64 result = client->write(packet);
            if (result == -1) {
                emit errorOccurred(QString("向客户端 %1:%2 发送数据失败: %3")
                                  .arg(client->peerAddress().toString())
                                  .arg(client->peerPort())
                                  .arg(client->errorString()));
                clientsToRemove.append(client);
            }
        } else {
            clientsToRemove.append(client);
        }
    }
    
    // 移除已断开的客户端
    for (QTcpSocket *client : clientsToRemove) {
        m_clients.removeAll(client);
        client->deleteLater();
    }
    
    if (!m_clients.isEmpty()) {
        emit messageSent(QString("%1 向 %2 个客户端发送数据 (Packet size: %3 bytes)")
                        .arg(m_name)
                        .arg(m_clients.size())
                        .arg(packet.size()));
    }
}

void TCPServerSimulator::onNewConnection()
{
    while (m_tcpServer->hasPendingConnections()) {
        QTcpSocket *clientSocket = m_tcpServer->nextPendingConnection();
        connect(clientSocket, &QTcpSocket::disconnected, this, &TCPServerSimulator::onClientDisconnected);
#if QT_VERSION >= QT_VERSION_CHECK(6, 0, 0)
        connect(clientSocket, &QTcpSocket::errorOccurred, this, &TCPServerSimulator::onError);
#else
        connect(clientSocket, QOverload<QAbstractSocket::SocketError>::of(&QTcpSocket::error),
                this, &TCPServerSimulator::onError);
#endif
        
        m_clients.append(clientSocket);
        
        emit messageSent(QString("%1: 新客户端连接 %2:%3")
                        .arg(m_name)
                        .arg(clientSocket->peerAddress().toString())
                        .arg(clientSocket->peerPort()));
    }
}

void TCPServerSimulator::onClientDisconnected()
{
    QTcpSocket *clientSocket = qobject_cast<QTcpSocket*>(sender());
    if (clientSocket) {
        m_clients.removeAll(clientSocket);
        clientSocket->deleteLater();
        
        emit messageSent(QString("%1: 客户端断开连接，当前连接数: %2")
                        .arg(m_name)
                        .arg(m_clients.size()));
    }
}

void TCPServerSimulator::onError(QAbstractSocket::SocketError socketError)
{
    Q_UNUSED(socketError);
    QTcpSocket *socket = qobject_cast<QTcpSocket*>(sender());
    QTcpServer *server = qobject_cast<QTcpServer*>(sender());
    
    if (socket) {
        emit errorOccurred(QString("TCP客户端错误 %1: %2")
                          .arg(m_name)
                          .arg(socket->errorString()));
    } else if (server) {
        emit errorOccurred(QString("TCP服务器错误 %1: %2")
                          .arg(m_name)
                          .arg(m_tcpServer->errorString()));
    }
}
