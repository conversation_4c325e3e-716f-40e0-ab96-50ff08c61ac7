<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DeviceListPanel</class>
 <widget class="QWidget" name="DeviceListPanel">
  <layout class="QVBoxLayout" name="mainLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item>
    <widget class="QFrame" name="frame">
     <property name="styleSheet">
      <string notr="true">QFrame {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
}</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="contentWidget" native="true">
        <layout class="QVBoxLayout" name="contentLayout">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <property name="spacing">
          <number>0</number>
         </property>
         <item>
          <widget class="QScrollArea" name="scrollArea">
           <property name="widgetResizable">
            <bool>true</bool>
           </property>
           <property name="styleSheet">
            <string notr="true">QScrollArea {
                border: none;
                background: transparent;
                }
                QScrollBar:vertical {
                border: none;
                background: #f1f3f4;
                width: 8px;
                border-radius: 4px;
                margin: 0px 2px;
                }
                QScrollBar::handle:vertical {
                background: #dadce0;
                border-radius: 4px;
                min-height: 20px;
                }
                QScrollBar::handle:vertical:hover {
                background: #bdc1c6;
                }</string>
           </property>
           <widget class="QWidget" name="devicesContainer">
            <property name="styleSheet">
             <string notr="true">QWidget {
                background-color: #ffffff;
                border: none;
                border-radius: 6px;
                }
            </string>
            </property>
            <layout class="QGridLayout" name="devicesLayout">
             <property name="leftMargin">
              <number>10</number>
             </property>
             <property name="topMargin">
              <number>10</number>
             </property>
             <property name="rightMargin">
              <number>10</number>
             </property>
             <property name="bottomMargin">
              <number>10</number>
             </property>
             <property name="horizontalSpacing">
              <number>15</number>
             </property>
             <property name="verticalSpacing">
              <number>15</number>
             </property>
             <property name="alignment">
              <set>Qt::AlignTop|Qt::AlignLeft</set>
             </property>
            </layout>
           </widget>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>