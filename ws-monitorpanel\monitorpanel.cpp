// ws-monitorpanel/monitorpanel.cpp
#include "monitorpanel.h"
#include <QVBoxLayout>
#include <QGroupBox>
#include <QDir>
#include <QCoreApplication>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDateTime>
#include <QMessageBox>
#include <QTimer>
#include "utils/monitorutils.h"
// #include "monitor_data_client.h"
#include "../workstation/device_connection_manager.h"

MonitorPanel::MonitorPanel(const QString &deviceId, const QString &deviceIP, const quint16 &devicePort,
                          DeviceConnectionManager *connectionManager, QWidget *parent)
    : QWidget(parent)
    , m_addMonitorButton(nullptr)
    , m_monitorScrollArea(nullptr)
    , m_monitor<PERSON>ontainer(nullptr)
    , m_monitorLayout(nullptr)
    , m_connectionManager(connectionManager)  // 使用共享连接管理器
    , m_targetId(deviceId)
    , m_targetIP(deviceIP)
    , m_targetPort(devicePort)
    , m_isConnected(false)
    , m_plotTabWidget(nullptr)
{
    setupUI();
    setupMonitorItems();
  
    // 连接共享管理器的信号
    connect(m_connectionManager, &DeviceConnectionManager::monitorDataReceived,
            this, &MonitorPanel::onMonitorDataReceived);
    connect(m_connectionManager, &DeviceConnectionManager::connectionStateChanged,
            this, &MonitorPanel::onConnectionStateChanged);
    connect(m_connectionManager, &DeviceConnectionManager::errorOccurred,
            this, &MonitorPanel::onErrorOccurred);
            
    // 如果已经连接，开始订阅数据
    if (m_connectionManager->isConnected()) {
        startAutoSubscription();
    }
}

void MonitorPanel::onMonitorDataReceived(const QJsonObject &data)
{
    updateMonitorCardValues(data);
    
    // 更新所有曲线图
    for (PlotWidget *plot : m_plotWidgets.values()) {
        plot->updatePlotData(data);
    }
}

void MonitorPanel::onConnectionStateChanged(bool connected)
{
    m_isConnected = connected;
    
    if (connected) {
        startAutoSubscription();
    } 
}

void MonitorPanel::onErrorOccurred(const QString &error)
{
    QMessageBox::warning(this, "监控数据错误", error);
}

MonitorPanel::~MonitorPanel()
{
    // 在析构函数中显式断开所有连接
    // disconnect(m_connectionManager, nullptr, this, nullptr);
    
    // 清理监控卡片
    qDeleteAll(m_monitorCards);
    m_monitorCards.clear();
    
    // 清理曲线图组件
    qDeleteAll(m_plotWidgets);
    m_plotWidgets.clear();
}

void MonitorPanel::attemptReconnect()
{
    if (!m_isConnected) {
        //m_monitorDataClient->connectToServer(m_targetIP, m_targetPort);
    }
}

void MonitorPanel::setupMonitorItems()
{
    // 初始化可添加的监控项列表
    m_availableMonitorItems << "CPU使用率" 
                           << "内存使用率" 
                           << "硬盘使用率"
                           << "网络速度" ;
    
    // 初始化已添加的监控项（开始为空）
    m_addedMonitorItems.clear();
}

QStringList MonitorPanel::getRequiredDataTypes() const
{
    QStringList dataTypes;
    dataTypes << "cpu" << "memory" << "disk" << "network";
    return dataTypes;
}

void MonitorPanel::startAutoSubscription()
{
    QStringList dataTypes = getRequiredDataTypes();
    if (!dataTypes.isEmpty()) {
        m_connectionManager->requestMonitorData(dataTypes, 10); // 每秒更新一次
    }
}

QString MonitorPanel::getConfigFilePath() const
{
    QString appDir = QCoreApplication::applicationDirPath();
    QString devicesDir = appDir + "/devices";
    QString deviceDir = devicesDir + "/" + m_targetId;
    return deviceDir + "/monitor_config.json";
}

void MonitorPanel::loadMonitorConfig()
{
    QString configPath = getConfigFilePath();
    QFile configFile(configPath);
    
    if (!configFile.exists()) {
        return; // 配置文件不存在，使用默认配置
    }
    
    if (!configFile.open(QIODevice::ReadOnly)) {
        QMessageBox::warning(this, "错误", "无法读取监控配置: " + configPath);
        return;
    }
    
    QByteArray jsonData = configFile.readAll();
    configFile.close();
    
    QJsonDocument doc = QJsonDocument::fromJson(jsonData);
    if (!doc.isObject()) {
        QMessageBox::warning(this, "错误", "监控配置文件格式错误");
        return;
    }
    
    QJsonObject config = doc.object();
    if (config.contains("monitors") && config["monitors"].isArray()) {
        QJsonArray monitorsArray = config["monitors"].toArray();
        m_addedMonitorItems.clear();
        
        for (int i = 0; i < monitorsArray.size(); ++i) {
            m_addedMonitorItems.append(monitorsArray[i].toString());
        }
    }
    
    // 根据加载的配置创建监控卡片
    for (const QString &item : m_addedMonitorItems) {
        createMonitorCard(item);
    }
}

void MonitorPanel::saveMonitorConfig()
{
    QString configPath = getConfigFilePath();
    QFile configFile(configPath);
    
    if (!configFile.open(QIODevice::WriteOnly)) {
        QMessageBox::warning(this, "错误", "无法保存监控配置: " + configPath);
        return;
    }
    
    QJsonObject config;
    config["deviceIP"] = m_targetIP;
    config["lastUpdated"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    QJsonArray monitorsArray;
    for (const QString &monitor : m_addedMonitorItems) {
        monitorsArray.append(monitor);
    }
    config["monitors"] = monitorsArray;
    
    QJsonDocument doc(config);
    configFile.write(doc.toJson());
    configFile.close();
}

void MonitorPanel::setupUI()
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(10);

    // 监控卡片区域
    QWidget *monitorWidget = new QWidget();
    QHBoxLayout *monitorLayout = new QHBoxLayout(monitorWidget);
    monitorLayout->setContentsMargins(0, 0, 0, 0);

    m_addMonitorButton = new AddMonitorCard(this);
    
    m_monitorScrollArea = new QScrollArea(this);
    m_monitorScrollArea->setWidgetResizable(true);
    m_monitorScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_monitorScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    
    m_monitorContainer = new QWidget();
    m_monitorLayout = new QHBoxLayout(m_monitorContainer);
    m_monitorLayout->setSpacing(20);
    m_monitorLayout->setContentsMargins(10, 10, 10, 10);
    m_monitorLayout->addWidget(m_addMonitorButton);
    m_monitorLayout->addStretch();
    
    m_monitorScrollArea->setWidget(m_monitorContainer);
    
    monitorLayout->addWidget(m_monitorScrollArea);
    
    // 曲线图区域
    m_plotTabWidget = new QTabWidget();
    m_plotTabWidget->setStyleSheet(
        "QTabWidget::pane {"
        "  border: 1px solid #e0e0e0;"
        "  border-radius: 4px;"
        "}"
        "QTabBar::tab {"
        "  background: #f0f0f0;"
        "  border: 1px solid #e0e0e0;"
        "  border-bottom-color: #e0e0e0;"
        "  border-top-left-radius: 4px;"
        "  border-top-right-radius: 4px;"
        "  min-width: 8ex;"
        "  padding: 4px;"
        "}"
        "QTabBar::tab:selected, QTabBar::tab:hover {"
        "  background: #ffffff;"
        "}"
        "QTabBar::tab:selected {"
        "  border-bottom-color: #ffffff;"
        "}"
    );
    
    // 添加默认的曲线图标签页
    PlotWidget *cpuPlot = new PlotWidget();
    cpuPlot->setDataType("cpu");
    m_plotWidgets["cpu"] = cpuPlot;
    m_plotTabWidget->addTab(cpuPlot, "CPU使用率");
    
    PlotWidget *memoryPlot = new PlotWidget();
    memoryPlot->setDataType("memory");
    m_plotWidgets["memory"] = memoryPlot;
    m_plotTabWidget->addTab(memoryPlot, "内存使用率");
    
    PlotWidget *diskPlot = new PlotWidget();
    diskPlot->setDataType("disk");
    m_plotWidgets["disk"] = diskPlot;
    m_plotTabWidget->addTab(diskPlot, "硬盘使用率");
    
    PlotWidget *networkPlot = new PlotWidget();
    networkPlot->setDataType("network");
    m_plotWidgets["network"] = networkPlot;
    m_plotTabWidget->addTab(networkPlot, "网络速度");
    
    // 连接曲线图到数据更新信号
    connect(this, &MonitorPanel::onMonitorDataReceived, cpuPlot, &PlotWidget::updatePlotData);
    connect(this, &MonitorPanel::onMonitorDataReceived, memoryPlot, &PlotWidget::updatePlotData);
    connect(this, &MonitorPanel::onMonitorDataReceived, diskPlot, &PlotWidget::updatePlotData);
    connect(this, &MonitorPanel::onMonitorDataReceived, networkPlot, &PlotWidget::updatePlotData);
    
    mainLayout->addWidget(monitorWidget, 1);
    mainLayout->addWidget(m_plotTabWidget, 2);
    
    connect(m_addMonitorButton, &AddMonitorCard::clicked, this, &MonitorPanel::onAddMonitorButtonClicked);
}

void MonitorPanel::onAddMonitorButtonClicked()
{
    MonitorSelector selector(m_availableMonitorItems, m_addedMonitorItems, this);
    int result = selector.exec();
    
    if (result == QDialog::Accepted) {
        QList<QString> selectedItems = selector.getSelectedItems();
        
        if (!selectedItems.isEmpty()) {
            // 更新已添加的监控项列表
            QStringList newlyAddedItems;
            for (const QString &item : selectedItems) {
                if (!m_addedMonitorItems.contains(item)) {
                    m_addedMonitorItems.append(item);
                    newlyAddedItems.append(item);
                }
            }
            
            // 为新添加的项目创建控件并添加到布局中
            if (m_monitorLayout) {
                for (const QString &item : newlyAddedItems) {
                    createMonitorCard(item);
                }
            }
            
            saveMonitorConfig();
        }
    }
}

void MonitorPanel::updateMonitorCardValues(const QJsonObject &data)
{
    // 根据收到的数据更新监控卡片的值
    for (auto it = m_monitorCards.constBegin(); it != m_monitorCards.constEnd(); ++it) {
        const QString &itemName = it.key();
        MonitorCard *card = it.value();

        if (!card) {
            continue;
        }
        
        if (itemName.contains("CPU", Qt::CaseInsensitive) && data.contains("cpu")) {
            card->setValue(data["cpu"].toInt());
        } else if (itemName.contains("内存", Qt::CaseInsensitive) && data.contains("memory")) {
            // 假设内存值以MB为单位发送
            card->setValue(data["memory"].toInt());
        } else if (itemName.contains("硬盘", Qt::CaseInsensitive) && data.contains("disk")) {
            // 假设硬盘值以GB为单位发送
            card->setValue(data["disk"].toInt());
        } else if (itemName.contains("网络", Qt::CaseInsensitive) && data.contains("network")) {
            // 假设网络值以KB/s为单位发送
            card->setValue(data["network"].toInt());
        }
    }
}

void MonitorPanel::createMonitorCard(const QString &item)
{
    // 根据监控项名称选择合适的颜色策略和数据类型
    MonitorCard::ColorStrategy colorStrategy = MonitorUtils::cpuColorStrategy;
    MonitorCard::DataType dataType = MonitorCard::Percentage;
    
    if (item.contains("内存")) {
        colorStrategy = MonitorUtils::memoryColorStrategy;
        dataType = MonitorCard::Memory;
    } else if (item.contains("硬盘")) {
        colorStrategy = MonitorUtils::diskColorStrategy;
        dataType = MonitorCard::Disk;
    } else if (item.contains("网络")) {
        colorStrategy = MonitorUtils::networkColorStrategy;
        dataType = MonitorCard::Network;
    } else if (item.contains("温度")) {
        colorStrategy = MonitorUtils::temperatureColorStrategy;
    }
    
    MonitorCard *newMonitorCard = new MonitorCard(item, colorStrategy, dataType, this);
    newMonitorCard->setValue(0); // 初始化值
    newMonitorCard->setDataType(dataType); // 设置数据类型
    
    // 保存监控卡片引用
    m_monitorCards[item] = newMonitorCard;

    connect(newMonitorCard, &MonitorCard::deleted, this, &MonitorPanel::onMonitorCardDeleted);
    
    // 在添加按钮之前插入新控件
    int addButtonIndex = m_monitorLayout->indexOf(m_addMonitorButton);
    if (addButtonIndex != -1) {
        m_monitorLayout->insertWidget(addButtonIndex, newMonitorCard);
    } else {
        m_monitorLayout->addWidget(newMonitorCard);
    }
}

void MonitorPanel::onMonitorCardDeleted(const QString& cardLabel)
{
    // 从映射中移除已删除的卡片
    m_monitorCards.remove(cardLabel);
    
    // 从已添加的监控项列表中移除
    m_addedMonitorItems.removeOne(cardLabel);
    
    // 发出监控项变化信号
    saveMonitorConfig();
}
