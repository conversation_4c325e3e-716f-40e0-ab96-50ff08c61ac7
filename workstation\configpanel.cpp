#include "configpanel.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGroupBox>
#include <QTreeWidget>
#include <QPushButton>
#include <QMessageBox>
#include <QDir>
#include <QTreeWidgetItem>
#include <QHeaderView>
#include <QDateTime>
#include <QJsonDocument>
#include <QJsonArray>
#include <QSplitter>
#include <QCoreApplication>
#include <QFileDialog>
#include <QLabel>
#include <QProgressDialog>
#include <QStandardPaths>
#include <QProcess>
#include <QTemporaryDir>
#include <QMenu>
#include <QDesktopServices>
#include <QFile>
#include <QXmlStreamReader>
#include <QTextEdit>
#include <QApplication>
#include <QClipboard>
#include <QCloseEvent>
#include "device_connection_manager.h"

// QFileContentWindow 实现
QFileContentWindow::QFileContentWindow(QWidget *parent)
    : QWidget(parent)
{
    setWindowTitle("文件内容");
    resize(800, 600);
    
    QVBoxLayout *layout = new QVBoxLayout(this);
    
    m_contentDisplay = new QTextEdit();
    m_contentDisplay->setReadOnly(true);
    
    layout->addWidget(m_contentDisplay);
}

void QFileContentWindow::setContent(const QString &content, const QString &fileName)
{
    m_fileName = fileName;
    setWindowTitle(QString("文件内容 - %1").arg(fileName));
    m_contentDisplay->setPlainText(content);
}

void QFileContentWindow::closeEvent(QCloseEvent *event)
{
    emit windowClosed();
    QWidget::closeEvent(event);
}


ConfigPanel::ConfigPanel(const QString& deviceId, const QString& deviceIP, int devicePort,
                        DeviceConnectionManager *connectionManager, QWidget *parent)
    : QWidget(parent)
    , m_configFileTreeWidget(nullptr)
    , m_downloadButton(nullptr)
    , m_deviceId(deviceId)
    , m_deviceIP(deviceIP)
    , m_devicePort(devicePort)
    , m_connectionManager(connectionManager)
    , m_progressDialog(nullptr)
    , m_fileContentWindow(nullptr) // 初始化文件内容窗口指针
{
    setupUI();
    
    // 加载本地已存在的配置文件
    loadLocalConfigFiles();
    
    // 连接共享管理器的信号
    connect(m_connectionManager, &DeviceConnectionManager::configDetailsReceived,
            this, &ConfigPanel::onConfigBundleReceived);
    connect(m_connectionManager, &DeviceConnectionManager::connectionStateChanged,
            this, &ConfigPanel::onConnectionStateChanged);
    connect(m_connectionManager, &DeviceConnectionManager::errorOccurred,
            this, &ConfigPanel::onErrorOccurred);
            
    // 连接文件传输相关信号
    connect(m_connectionManager, &DeviceConnectionManager::configFileTransferStarted,
            this, &ConfigPanel::onConfigFileTransferStarted);
    connect(m_connectionManager, &DeviceConnectionManager::configFileTransferProgress,
            this, &ConfigPanel::onConfigFileTransferProgress);
    connect(m_connectionManager, &DeviceConnectionManager::configFileReceived,
            this, &ConfigPanel::onConfigFileReceived);
}

ConfigPanel::~ConfigPanel()
{
    if (m_progressDialog) {
        delete m_progressDialog;
    }
    
    // 删除文件内容窗口
    if (m_fileContentWindow) {
        delete m_fileContentWindow;
    }
}


void ConfigPanel::setupUI()
{
    // 创建主垂直分割器
    m_mainVerticalSplitter = new QSplitter(Qt::Vertical, this);
    
    // 创建上半部分水平分割器（原有的左右分割）
    QSplitter *mainSplitter = new QSplitter(Qt::Horizontal);
    
    // 左侧区域 - 文件列表
    QWidget *leftWidget = new QWidget();
    QVBoxLayout *leftLayout = new QVBoxLayout(leftWidget);
    
    // 下载按钮
    m_downloadButton = new QPushButton("下载服务端配置");
    m_downloadButton->setStyleSheet("QPushButton { padding: 8px; font-weight: bold; }");
    
    // 配置文件树形结构
    QGroupBox *configFilesGroup = new QGroupBox("配置文件列表");
    QVBoxLayout *filesLayout = new QVBoxLayout(configFilesGroup);
    m_configFileTreeWidget = new QTreeWidget();
    m_configFileTreeWidget->setColumnCount(3); // 减少到3列（去掉权限列）
    m_configFileTreeWidget->setHeaderLabels(QStringList() << "文件名" << "大小" << "修改时间");
    m_configFileTreeWidget->setContextMenuPolicy(Qt::CustomContextMenu);
    
    // 设置列宽，让文件名列更宽
    m_configFileTreeWidget->setColumnWidth(0, 250); // 文件名列
    m_configFileTreeWidget->setColumnWidth(1, 70); // 大小列
    m_configFileTreeWidget->setColumnWidth(2, 150); // 修改时间列
    
    filesLayout->addWidget(m_configFileTreeWidget);
    
    leftLayout->addWidget(m_downloadButton);
    leftLayout->addWidget(configFilesGroup);
    
    // 右侧区域 - 文件结构显示
    QWidget *rightWidget = new QWidget();
    QVBoxLayout *rightLayout = new QVBoxLayout(rightWidget);
    
    // 文件结构标题
    QLabel *fileTreeLabel = new QLabel("配置包文件结构");
    fileTreeLabel->setStyleSheet("font-weight: bold; font-size: 14px;");
    
    // 文件结构树
    m_fileListTreeWidget = new QTreeWidget();
    m_fileListTreeWidget->setColumnCount(3);
    m_fileListTreeWidget->setHeaderLabels(QStringList() << "文件名" << "大小" << "修改时间");
    
    // 设置列宽
    m_fileListTreeWidget->setColumnWidth(0, 200); // 文件名列
    m_fileListTreeWidget->setColumnWidth(1, 100); // 大小列
    m_fileListTreeWidget->setColumnWidth(2, 150); // 修改时间列
    
    rightLayout->addWidget(fileTreeLabel);
    rightLayout->addWidget(m_fileListTreeWidget);
    
    // 添加到水平分割器
    mainSplitter->addWidget(leftWidget);
    mainSplitter->addWidget(rightWidget);
    mainSplitter->setSizes(QList<int>() << 550 << 450);
    
    // 创建下半部分 - 文件内容显示区域（增加高度）
    QWidget *bottomWidget = new QWidget();
    QVBoxLayout *bottomLayout = new QVBoxLayout(bottomWidget);
    
    QLabel *contentLabel = new QLabel("文件内容");
    contentLabel->setStyleSheet("font-weight: bold; font-size: 14px;");
    
    m_fileContentDisplay = new QTextEdit();
    m_fileContentDisplay->setReadOnly(true);
    
    bottomLayout->addWidget(contentLabel);
    bottomLayout->addWidget(m_fileContentDisplay);
    
    // 添加到垂直分割器（调整高度比例，让内容区域更大）
    m_mainVerticalSplitter->addWidget(mainSplitter);
    m_mainVerticalSplitter->addWidget(bottomWidget);
    m_mainVerticalSplitter->setSizes(QList<int>() << 300 << 400); // 调整比例
    
    // 主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->addWidget(m_mainVerticalSplitter);
    
    // 连接信号槽
    connect(m_downloadButton, &QPushButton::clicked, this, &ConfigPanel::onDownloadConfig);
    connect(m_configFileTreeWidget, &QTreeWidget::itemClicked, this, &ConfigPanel::onConfigFileItemClicked);
    connect(m_configFileTreeWidget, &QTreeWidget::itemDoubleClicked, this, &ConfigPanel::onConfigFileItemDoubleClicked);
    connect(m_configFileTreeWidget, &QTreeWidget::customContextMenuRequested, this, &ConfigPanel::onConfigFileItemRightClicked);
    connect(m_fileListTreeWidget, &QTreeWidget::itemClicked, this, &ConfigPanel::onBundleFileItemClicked);
}

void ConfigPanel::onDownloadConfig()
{
    if (!m_connectionManager->isConnected()) {
        QMessageBox::warning(this, "未连接", "与设备未连接，请先建立连接");
        return;
    }
    
    // 发送 CONFIG_BUNDLE 命令获取配置压缩包
    m_connectionManager->sendCommand("CONFIG_BUNDLE");
    
    // 禁用按钮防止重复点击
    m_downloadButton->setEnabled(false);
    m_downloadButton->setText("正在下载...");
}

void ConfigPanel::onConfigBundleReceived(const QJsonArray &details)
{
    // 恢复按钮状态
    m_downloadButton->setEnabled(true);
    m_downloadButton->setText("下载服务端配置");
    
    // 显示配置文件信息
    m_currentConfigDetails = details;
    m_configFileTreeWidget->clear();
    
    for (const QJsonValue &value : details) {
        if (value.isObject()) {
            QJsonObject fileObj = value.toObject();
            
            QTreeWidgetItem *item = new QTreeWidgetItem(m_configFileTreeWidget);
            item->setText(0, fileObj["name"].toString());
            
            // 格式化文件大小显示
            qint64 size = fileObj["size"].toString().toLongLong();
            QString sizeStr;
            if (size < 1024) {
                sizeStr = QString("%1 B").arg(size);
            } else if (size < 1024 * 1024) {
                sizeStr = QString("%1 KB").arg(size / 1024.0, 0, 'f', 1);
            } else if (size < 1024 * 1024 * 1024) {
                sizeStr = QString("%1 MB").arg(size / (1024.0 * 1024.0), 0, 'f', 1);
            } else {
                sizeStr = QString("%1 GB").arg(size / (1024.0 * 1024.0 * 1024.0), 0, 'f', 1);
            }
            item->setText(1, sizeStr);
            
            item->setText(2, fileObj["modified"].toString());
            
            // 保存原始数据到UserRole
            item->setData(0, Qt::UserRole, fileObj);
        }
    }
    
    // 如果有文件，自动选择第一个文件
    if (m_configFileTreeWidget->topLevelItemCount() > 0) {
        m_configFileTreeWidget->setCurrentItem(m_configFileTreeWidget->topLevelItem(0));
        onConfigFileItemClicked(m_configFileTreeWidget->topLevelItem(0), 0);
    }
    
    QMessageBox::information(this, "下载完成", "配置文件列表已更新");
}

void ConfigPanel::onConnectionStateChanged(bool connected)
{
    if (connected) {
        m_downloadButton->setEnabled(true);
        m_downloadButton->setText("下载服务端配置");
    } else {
        m_downloadButton->setEnabled(false);
        m_downloadButton->setText("未连接");
        m_configFileTreeWidget->clear();
        // m_contentDisplay->clear(); // 清空内容显示
    }
}

void ConfigPanel::onErrorOccurred(const QString &error)
{
    // 恢复按钮状态
    m_downloadButton->setEnabled(true);
    m_downloadButton->setText("下载服务端配置");
    
    // 关闭进度对话框（如果存在）
    if (m_progressDialog) {
        m_progressDialog->close();
        m_progressDialog = nullptr;
    }
    
    QMessageBox::warning(this, "错误", "操作失败: " + error);
}

void ConfigPanel::onConfigFileTransferStarted(qint64 fileSize)
{
    // 创建进度对话框
    m_progressDialog = new QProgressDialog("正在下载配置文件...", "取消", 0, 100, this);
    m_progressDialog->setWindowModality(Qt::WindowModal);
    m_progressDialog->setMinimumDuration(0);
    m_progressDialog->setValue(0);
    
    connect(m_progressDialog, &QProgressDialog::canceled, [this]() {
        m_connectionManager->cancelConfigFileReception();
        m_downloadButton->setEnabled(true);
        m_downloadButton->setText("下载服务端配置");
    });
    
    m_progressDialog->show();
}

void ConfigPanel::onConfigFileTransferProgress(qint64 bytesReceived, qint64 totalBytes)
{
    if (m_progressDialog) {
        int percentage = static_cast<int>((bytesReceived * 100) / totalBytes);
        m_progressDialog->setValue(percentage);
        
        QString info = QString("已接收: %1 / %2 字节")
                      .arg(bytesReceived)
                      .arg(totalBytes);
        m_progressDialog->setLabelText(QString("正在下载配置文件...\n%1").arg(info));
    }
}

void ConfigPanel::onConfigFileReceived(const QString &filePath)
{
    // 关闭进度对话框
    if (m_progressDialog) {
        m_progressDialog->close();
        m_progressDialog = nullptr;
    }
    
    // 恢复按钮状态
    m_downloadButton->setEnabled(true);
    m_downloadButton->setText("下载服务端配置");
    
    // 获取设备配置目录
    QString appDir = QCoreApplication::applicationDirPath();
    QString deviceConfigDir = appDir + "/devices/" + m_deviceId + "/config";
    
    // 创建设备配置目录（如果不存在）
    QDir dir(deviceConfigDir);
    if (!dir.exists()) {
        dir.mkpath(".");
    }
    
    // 生成目标文件名（保持原始文件名）
    QFileInfo fileInfo(filePath);
    QString targetFilePath = deviceConfigDir + "/" + fileInfo.fileName();
    
    // 如果目标文件已存在，添加序号
    QString finalTargetPath = targetFilePath;
    int counter = 1;
    while (QFile::exists(finalTargetPath)) {
        QString baseName = fileInfo.baseName();
        QString suffix = fileInfo.suffix();
        finalTargetPath = deviceConfigDir + "/" + baseName + "_" + QString::number(counter) + "." + suffix;
        counter++;
    }
    
    // 移动文件到设备配置目录
    if (QFile::rename(filePath, finalTargetPath)) {
        QMessageBox::information(this, "下载完成", 
                                QString("配置文件已成功保存到设备配置目录:\n%1").arg(finalTargetPath));
        
        // 重新加载文件列表
        loadLocalConfigFiles();
    } else {
        QMessageBox::warning(this, "错误", 
                            QString("无法将配置文件移动到设备目录:\n源文件: %1\n目标文件: %2")
                            .arg(filePath, finalTargetPath));
    }
}

void ConfigPanel::onConfigFileItemClicked(QTreeWidgetItem *item, int column)
{
    Q_UNUSED(column);
    if (item) {
        QVariant data = item->data(0, Qt::UserRole);
        if (data.canConvert<QJsonObject>()) {
            QJsonObject bundleInfo = data.toJsonObject();
            // displayConfigBundleContents(bundleInfo);
            
            // 显示包内完整文件树结构
            QString bundleName = bundleInfo["name"].toString();
            QString appDir = QCoreApplication::applicationDirPath();
            QString bundlePath = appDir + "/devices/" + m_deviceId + "/config/" + bundleName;
            displayBundleFileTree(bundlePath);  // 使用新的方法
        }
    }
}

void ConfigPanel::displayBundleFileTree(const QString &bundlePath)
{
    m_fileListTreeWidget->clear();
    
    // 检查文件是否存在
    QFile bundleFile(bundlePath);
    if (!bundleFile.exists()) {
        QTreeWidgetItem *item = new QTreeWidgetItem(m_fileListTreeWidget);
        item->setText(0, "错误: 文件不存在");
        return;
    }
    
    // 创建临时目录用于解压
    QString tempDir = QDir::tempPath() + "/config_bundle_" + QString::number(QDateTime::currentMSecsSinceEpoch());
    QDir().mkpath(tempDir);
    
#ifdef Q_OS_WIN
    // Windows 使用 PowerShell 解压到临时目录
    QString program = "powershell";
    QStringList arguments;
    arguments << "-Command" << QString("Expand-Archive -Path '%1' -DestinationPath '%2' -Force").arg(bundlePath, tempDir);
    
    QProcess process;
    process.start(program, arguments);
    process.waitForFinished(30000);
    
    if (process.exitCode() == 0) {
        // 填充文件树
        populateTreeFromDirectory(tempDir);
    } else {
        QTreeWidgetItem *item = new QTreeWidgetItem(m_fileListTreeWidget);
        item->setText(0, "错误: 无法解压文件");
        item->setText(1, process.readAllStandardError());
    }
#else
    // Linux/Mac 使用 unzip 命令解压
    QString program = "unzip";
    QStringList arguments;
    arguments << "-o" << bundlePath << "-d" << tempDir;
    
    QProcess process;
    process.start(program, arguments);
    process.waitForFinished(30000);
    
    if (process.exitCode() == 0) {
        // 填充文件树
        populateTreeFromDirectory(tempDir);
    } else {
        QTreeWidgetItem *item = new QTreeWidgetItem(m_fileListTreeWidget);
        item->setText(0, "错误: 无法解压文件");
        item->setText(1, process.readAllStandardError());
    }
#endif
    
    // 清理临时目录
    QDir(tempDir).removeRecursively();
}

void ConfigPanel::populateTreeFromDirectory(const QString &dirPath, QTreeWidgetItem *parentItem)
{
    QDir dir(dirPath);
    QFileInfoList fileList = dir.entryInfoList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot, QDir::DirsFirst | QDir::Name);
    
    for (const QFileInfo &fileInfo : fileList) {
        QTreeWidgetItem *item;
        if (parentItem) {
            item = new QTreeWidgetItem(parentItem);
        } else {
            item = new QTreeWidgetItem(m_fileListTreeWidget);
        }
        
        item->setText(0, fileInfo.fileName());
        
        if (fileInfo.isDir()) {
            // 对于目录，显示"<DIR>"并递归处理
            item->setText(1, "<DIR>");
            item->setText(2, fileInfo.lastModified().toString(Qt::ISODate));
            
            // 递归处理子目录
            populateTreeFromDirectory(fileInfo.absoluteFilePath(), item);
        } else {
            // 对于文件，显示大小和修改时间
            qint64 size = fileInfo.size();
            QString sizeStr;
            if (size < 1024) {
                sizeStr = QString("%1 B").arg(size);
            } else if (size < 1024 * 1024) {
                sizeStr = QString("%1 KB").arg(size / 1024.0, 0, 'f', 1);
            } else if (size < 1024 * 1024 * 1024) {
                sizeStr = QString("%1 MB").arg(size / (1024.0 * 1024.0), 0, 'f', 1);
            } else {
                sizeStr = QString("%1 GB").arg(size / (1024.0 * 1024.0 * 1024.0), 0, 'f', 1);
            }
            item->setText(1, sizeStr);
            item->setText(2, fileInfo.lastModified().toString(Qt::ISODate));
        }
    }
}

void ConfigPanel::onConfigFileItemDoubleClicked(QTreeWidgetItem *item, int column)
{
    // 双击时也可以显示内容（与单击相同）
    onConfigFileItemClicked(item, column);
}

void ConfigPanel::onConfigFileItemRightClicked(const QPoint &pos)
{
    // 获取点击的项
    QTreeWidgetItem *item = m_configFileTreeWidget->itemAt(pos);
    if (item) {
        // 创建右键菜单
        QMenu menu(this);
        
        QAction *viewAction = menu.addAction("查看文件信息");
        QAction *openDirAction = menu.addAction("打开所在目录");
        QAction *deleteAction = menu.addAction("删除文件");
        QAction *copyNameAction = menu.addAction("复制文件名");
        
        QAction *selectedAction = menu.exec(m_configFileTreeWidget->viewport()->mapToGlobal(pos));
        
        QString filePath = item->data(0, Qt::UserRole).toJsonObject()["name"].toString();
        
        if (selectedAction == viewAction) {
            QVariant data = item->data(0, Qt::UserRole);
            if (data.canConvert<QJsonObject>()) {
                QJsonObject fileInfo = data.toJsonObject();
                QString details = "文件名: " + fileInfo["name"].toString() + "\n";
                details += "大小: " + fileInfo["size"].toString() + " 字节\n";
                details += "修改时间: " + fileInfo["modified"].toString() + "\n";
                details += "权限: " + fileInfo["permissions"].toString() + "\n";
                
                QMessageBox::information(this, "文件详情", details);
            }
        }
        else if (selectedAction == openDirAction) {
            // 打开文件所在目录
            QString appDir = QCoreApplication::applicationDirPath();
            QString configDir = appDir + "/devices/" + m_deviceId + "/config";
            QString fullFilePath = configDir + "/" + filePath;
            
            QFileInfo fileInfo(fullFilePath);
            QString dirPath = fileInfo.absolutePath();
#ifdef Q_OS_WIN
            QProcess::startDetached("explorer.exe", QStringList() << "/select," << QDir::toNativeSeparators(fullFilePath));
#elif defined(Q_OS_MAC)
            QProcess::startDetached("open", QStringList() << "-R" << fullFilePath);
#else
            QDesktopServices::openUrl(QUrl::fromLocalFile(dirPath));
#endif
        }
        else if (selectedAction == deleteAction) {
            QString appDir = QCoreApplication::applicationDirPath();
            QString configDir = appDir + "/devices/" + m_deviceId + "/config";
            QString fullFilePath = configDir + "/" + filePath;
            
            if (QMessageBox::question(this, "确认删除", 
                                     QString("确定要删除文件 %1 吗？").arg(filePath)) 
                == QMessageBox::Yes) {
                if (QFile::remove(fullFilePath)) {
                    QMessageBox::information(this, "删除成功", "文件已成功删除");
                    loadLocalConfigFiles(); // 重新加载文件列表
                    // m_contentDisplay->clear(); // 清空内容显示
                } else {
                    QMessageBox::warning(this, "删除失败", "无法删除文件");
                }
            }
        }
        else if (selectedAction == copyNameAction) {
            QApplication::clipboard()->setText(filePath);
        }
    }
}

void ConfigPanel::displayConfigBundleContents(const QJsonObject &bundleInfo)
{
    // 显示配置包的基本信息
    QString content = "配置包信息:\n";
    content += "========================\n";
    content += "文件名: " + bundleInfo["name"].toString() + "\n";
    content += "大小: " + bundleInfo["size"].toString() + " 字节\n";
    content += "修改时间: " + bundleInfo["modified"].toString() + "\n";
    content += "权限: " + bundleInfo["permissions"].toString() + "\n";
    content += "\n";
    content += "双击下载按钮可获取最新的配置文件列表。\n";
    
    // m_contentDisplay->setPlainText(content);
}

void ConfigPanel::extractAndLoadConfigBundle(const QString &zipFilePath)
{
    // 获取设备目录
    QString appDir = QCoreApplication::applicationDirPath();
    QString deviceDir = appDir + "/devices/" + m_deviceId;
    QString configDir = deviceDir + "/config";
    
    // 创建设备配置目录（如果不存在）
    QDir dir(configDir);
    if (!dir.exists()) {
        dir.mkpath(".");
    }
    
    // 解压ZIP文件到设备配置目录
#ifdef Q_OS_WIN
    QString program = "powershell";
    QStringList arguments;
    arguments << "-Command" << QString("Expand-Archive -Path '%1' -DestinationPath '%2'").arg(zipFilePath, configDir);
#else
    QString program = "unzip";
    QStringList arguments;
    arguments << "-o" << zipFilePath << "-d" << configDir;
#endif
    
    QProcess process;
    process.start(program, arguments);
    
    if (!process.waitForFinished(30000)) { // 等待最多30秒
        QMessageBox::warning(this, "错误", "解压配置文件超时");
        return;
    }
    
    if (process.exitCode() != 0) {
        QMessageBox::warning(this, "错误", QString("解压配置文件失败: %1").arg(process.readAllStandardError().constData()));
        return;
    }
    
    // 查找解压后的配置文件列表
    QStringList configFiles = dir.entryList(QDir::Files);
    
    // 构造配置文件信息数组
    QJsonArray configDetails;
    for (const QString &fileName : configFiles) {
        QFileInfo fileInfo(dir.filePath(fileName));
        QJsonObject fileObj;
        fileObj["name"] = fileName;
        fileObj["size"] = QString::number(fileInfo.size());
        fileObj["modified"] = fileInfo.lastModified().toString(Qt::ISODate);
        fileObj["permissions"] = "-rw-r--r--"; // 简化权限表示
        configDetails.append(fileObj);
    }
    
    // 显示配置文件信息
    onConfigBundleReceived(configDetails);
    
    QMessageBox::information(this, "下载完成", QString("配置文件已成功下载并解压到设备目录\n包含 %1 个文件").arg(configFiles.size()));
    
    // 删除临时ZIP文件
    QFile::remove(zipFilePath);
}

void ConfigPanel::loadLocalConfigFiles()
{
    // 获取设备配置目录路径
    QString appDir = QCoreApplication::applicationDirPath();
    QString configDir = appDir + "/devices/" + m_deviceId + "/config";
    
    QDir dir(configDir);
    if (!dir.exists()) {
        return;
    }
    
    // 清空当前显示
    m_configFileTreeWidget->clear();
    
    // 获取所有zip文件
    QStringList filters;
    filters << "*.zip";
    dir.setNameFilters(filters);
    
    QFileInfoList fileList = dir.entryInfoList(QDir::Files, QDir::Time);
    
    // 显示文件列表
    for (const QFileInfo &fileInfo : fileList) {
        if (fileInfo.fileName().startsWith("config_bundle_")) {
            QTreeWidgetItem *item = new QTreeWidgetItem(m_configFileTreeWidget);
            item->setText(0, fileInfo.fileName());
            
            // 格式化文件大小显示
            qint64 size = fileInfo.size();
            QString sizeStr;
            if (size < 1024) {
                sizeStr = QString("%1 B").arg(size);
            } else if (size < 1024 * 1024) {
                sizeStr = QString("%1 KB").arg(size / 1024.0, 0, 'f', 1);
            } else if (size < 1024 * 1024 * 1024) {
                sizeStr = QString("%1 MB").arg(size / (1024.0 * 1024.0), 0, 'f', 1);
            } else {
                sizeStr = QString("%1 GB").arg(size / (1024.0 * 1024.0 * 1024.0), 0, 'f', 1);
            }
            item->setText(1, sizeStr);
            
            item->setText(2, fileInfo.lastModified().toString(Qt::ISODate));
            
            // 保存文件信息到UserRole
            QJsonObject fileInfoObj;
            fileInfoObj["name"] = fileInfo.fileName();
            fileInfoObj["size"] = QString::number(fileInfo.size());
            fileInfoObj["modified"] = fileInfo.lastModified().toString(Qt::ISODate);
            fileInfoObj["permissions"] = "-rw-r--r--";
            item->setData(0, Qt::UserRole, fileInfoObj);
        }
    }
}

void ConfigPanel::extractAndDisplayConfigFiles(const QString &zipFilePath)
{
    // 获取设备目录
    QString appDir = QCoreApplication::applicationDirPath();
    QString deviceDir = appDir + "/devices/" + m_deviceId;
    QString configDir = deviceDir + "/config";
    
    // 创建设备配置目录（如果不存在）
    QDir dir(configDir);
    if (!dir.exists()) {
        dir.mkpath(".");
    }
    
    // 解压ZIP文件到设备配置目录
#ifdef Q_OS_WIN
    QString program = "powershell";
    QStringList arguments;
    arguments << "-Command" << QString("Expand-Archive -Path '%1' -DestinationPath '%2'").arg(zipFilePath, configDir);
#else
    QString program = "unzip";
    QStringList arguments;
    arguments << "-o" << zipFilePath << "-d" << configDir;
#endif
    
    QProcess process;
    process.start(program, arguments);
    
    if (!process.waitForFinished(30000)) { // 等待最多30秒
        QMessageBox::warning(this, "错误", "解压配置文件超时");
        return;
    }
    
    if (process.exitCode() != 0) {
        QMessageBox::warning(this, "错误", QString("解压配置文件失败: %1").arg(process.readAllStandardError().constData()));
        return;
    }
    
    // 查找解压后的配置文件列表
    QStringList configFiles = dir.entryList(QDir::Files);
    
    // 显示解压后的文件列表
    displayConfigFileList(configFiles);
    
    QMessageBox::information(this, "下载完成", QString("配置文件已成功下载并解压到设备目录\n包含 %1 个文件").arg(configFiles.size()));
    
    // 删除临时ZIP文件
    QFile::remove(zipFilePath);
}

void ConfigPanel::displayConfigFileList(const QStringList &fileList)
{
    // 清空当前显示
    m_configFileTreeWidget->clear();
    
    // 显示文件列表
    for (const QString &fileName : fileList) {
        QTreeWidgetItem *item = new QTreeWidgetItem(m_configFileTreeWidget);
        item->setText(0, fileName);
        item->setText(1, "文件"); // 类型
        // 可以添加更多文件信息，如大小、修改时间等
    }
    
    // 如果有文件，自动选择第一个文件
    if (m_configFileTreeWidget->topLevelItemCount() > 0) {
        m_configFileTreeWidget->setCurrentItem(m_configFileTreeWidget->topLevelItem(0));
    }
}

void ConfigPanel::onBundleFileItemClicked(QTreeWidgetItem *item, int column)
{
    Q_UNUSED(column);
    
    if (!item || item->text(1) == "<DIR>") {
        return;
    }
    
    // 获取当前选中的配置包
    QTreeWidgetItem *currentConfigItem = m_configFileTreeWidget->currentItem();
    if (!currentConfigItem) {
        QMessageBox::warning(this, "错误", "请先选择一个配置包");
        return;
    }
    
    QVariant data = currentConfigItem->data(0, Qt::UserRole);
    if (!data.canConvert<QJsonObject>()) {
        QMessageBox::warning(this, "错误", "无法获取配置包信息");
        return;
    }
    
    QJsonObject bundleInfo = data.toJsonObject();
    QString bundleName = bundleInfo["name"].toString();
    QString appDir = QCoreApplication::applicationDirPath();
    QString bundlePath = appDir + "/devices/" + m_deviceId + "/config/" + bundleName;
    
    // 获取选中的文件的完整路径（相对于包根目录）
    QStringList pathComponents;
    QTreeWidgetItem *currentItem = item;
    
    // 向上遍历到根节点，构建完整路径
    while (currentItem) {
    pathComponents.prepend(currentItem->text(0));
    currentItem = currentItem->parent();
    
    // 如果当前项在m_fileListTreeWidget的顶层，就停止遍历
    if (currentItem && currentItem->treeWidget() == m_fileListTreeWidget && 
        !currentItem->parent()) {
            // 确保包含顶层目录
            pathComponents.prepend(currentItem->text(0));
            break;
        }
    }
    
    // 构建相对于包根目录的文件路径
    QString relativeFilePath = pathComponents.join("/");
    
    // 创建临时目录用于解压
    QString tempDir = QDir::tempPath() + "/config_bundle_" + QString::number(QDateTime::currentMSecsSinceEpoch());
    QDir().mkpath(tempDir);
    
    // 解压配置包
#ifdef Q_OS_WIN
    QString program = "powershell";
    QStringList arguments;
    arguments << "-Command" << QString("Expand-Archive -Path '%1' -DestinationPath '%2' -Force").arg(bundlePath, tempDir);
    
    QProcess process;
    process.start(program, arguments);
    process.waitForFinished(30000);
    
    if (process.exitCode() != 0) {
        QMessageBox::warning(this, "错误", QString("解压失败: %1").arg(process.readAllStandardError().constData()));
        QDir(tempDir).removeRecursively();
        return;
    }
#else
    QString program = "unzip";
    QStringList arguments;
    arguments << "-o" << bundlePath << "-d" << tempDir;
    
    QProcess process;
    process.start(program, arguments);
    process.waitForFinished(30000);
    
    if (process.exitCode() != 0) {
        QMessageBox::warning(this, "错误", QString("解压失败: %1").arg(process.readAllStandardError().constData()));
        QDir(tempDir).removeRecursively();
        return;
    }
#endif
    
    // 读取文件内容（使用完整路径）
    QString filePath = tempDir + "/" + relativeFilePath;
    QFile file(filePath);
    
    if (!file.exists()) {
        QMessageBox::warning(this, "错误", QString("文件不存在: %1").arg(relativeFilePath));
        QDir(tempDir).removeRecursively();
        return;
    }
    
    if (file.open(QIODevice::ReadOnly)) {
        QByteArray content = file.readAll();
        file.close();
        
        // 检查是否为文本文件
        if (isTextFile(content)) {
            // 直接在主界面显示文件内容，而不是弹窗
            m_fileContentDisplay->setPlainText(QString::fromUtf8(content));
        } else {
            m_fileContentDisplay->setPlainText("这是一个二进制文件，无法显示内容");
        }
    } else {
        QMessageBox::warning(this, "错误", QString("无法打开文件: %1\n错误信息: %2")
                             .arg(relativeFilePath, file.errorString()));
    }
    
    // 清理临时目录
    QDir(tempDir).removeRecursively();
}

// void ConfigPanel::showFileContent(const QString &content, const QString &fileName)
// {
//     // 如果窗口已存在，先删除
//     if (m_fileContentWindow) {
//         delete m_fileContentWindow;
//     }
    
//     // 创建新的文件内容窗口
//     m_fileContentWindow = new QFileContentWindow();
//     m_fileContentWindow->setContent(content, fileName);
    
//     // 连接窗口关闭信号
//     connect(m_fileContentWindow, &QFileContentWindow::windowClosed, 
//             this, &ConfigPanel::onFileContentWindowClosed);
    
//     // 显示窗口
//     m_fileContentWindow->show();
//     m_fileContentWindow->raise();
//     m_fileContentWindow->activateWindow();
// }

// // 新增槽函数：处理文件内容窗口关闭
// void ConfigPanel::onFileContentWindowClosed()
// {
//     m_fileContentWindow = nullptr;
// }

// 添加辅助函数来判断是否为文本文件
bool ConfigPanel::isTextFile(const QByteArray &data)
{
    // 检查前1024个字节是否包含过多的null字符或控制字符
    int checkLength = qMin(data.size(), 1024);
    int controlCharCount = 0;
    
    for (int i = 0; i < checkLength; ++i) {
        char c = data[i];
        // 检查是否为控制字符（除了常见的换行符、制表符等）
        if ((c < 32 && c != '\n' && c != '\r' && c != '\t') || c == 127) {
            controlCharCount++;
        }
    }
    
    // 如果控制字符比例超过30%，则认为是二进制文件
    return (controlCharCount * 100 / checkLength) < 30;
}
