#ifndef SIMULATOR_H
#define SIMULATOR_H

#include <QObject>
#include <QTimer>
#include "PacketField.h"

class Simulator : public QObject
{
    Q_OBJECT
public:
    enum Type {
        TCP,
        TCPServer,
        UDP
    };
    
    explicit Simulator(QObject *parent = nullptr);
    virtual ~Simulator();
    
    virtual void start() = 0;
    virtual void stop() = 0;
    virtual Type type() const = 0;
    
    void setName(const QString &name);
    QString name() const;
    
    void setTargetAddress(const QString &address);
    QString targetAddress() const;
    
    void setTargetPort(quint16 port);
    quint16 targetPort() const;
    
    void setMessageInterval(int interval);
    int messageInterval() const;

    bool isRunning() const;

    void setPacketId(quint16 id) { m_packetId = id; }
    quint16 packetId() const { return m_packetId; }
    
    void setBigEndian(bool bigEndian) { m_bigEndian = bigEndian; }
    bool isBigEndian() const { return m_bigEndian; }
    
    void setPacketFields(const QList<PacketField>& fields) { m_packetFields = fields; }
    QList<PacketField> packetFields() const { return m_packetFields; }
    
    virtual QByteArray buildPacket() = 0;

signals:
    void messageSent(const QString &message);
    void errorOccurred(const QString &error);
    void configurationChanged(); 
protected:
    QString m_name;
    QString m_targetAddress;
    quint16 m_targetPort;
    int m_messageInterval;
    QTimer *m_timer;
    bool m_isRunning;

    quint32 m_packageCnt;

    quint16 m_packetId;
    bool m_bigEndian;
    QList<PacketField> m_packetFields;
};

#endif // SIMULATOR_H
