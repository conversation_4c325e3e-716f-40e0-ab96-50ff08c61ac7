<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>设备监控中心</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="mainLayout">
    <property name="leftMargin">
     <number>15</number>
    </property>
    <property name="topMargin">
     <number>15</number>
    </property>
    <property name="rightMargin">
     <number>15</number>
    </property>
    <property name="bottomMargin">
     <number>15</number>
    </property>
    <property name="spacing">
     <number>10</number>
    </property>
    <item>
     <layout class="QHBoxLayout" name="searchLayout">
      <property name="topMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QLineEdit" name="searchLineEdit">
        <property name="styleSheet">
        <string notr="true">QLineEdit {
        padding: 8px 12px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        background-color: #ffffff;
        selection-background-color: #4285f4;
        min-width: 200px;
        max-width: 250px;
        }
        QLineEdit:focus {
        border: 1px solid #4285f4;
        outline: none;
        }</string>
        </property>
        <property name="placeholderText">
        <string>搜索服务...</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <layout class="QHBoxLayout" name="contentLayout" stretch="4,1">
      <property name="spacing">
       <number>15</number>
      </property>
      <item>
       <layout class="QVBoxLayout" name="leftContentLayout">
        <property name="spacing">
         <number>10</number>
        </property>
        <item>
         <widget class="QWidget" name="deviceListPanelContainer" native="true">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>1</verstretch>
           </sizepolicy>
          </property>
          <layout class="QVBoxLayout" name="deviceListPanelLayout">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="spacing">
            <number>0</number>
           </property>
          </layout>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QWidget" name="detailsPanelContainer" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>1</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>300</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>300</width>
          <height>16777215</height>
         </size>
        </property>
        <layout class="QVBoxLayout" name="detailsPanelLayout">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <property name="spacing">
          <number>0</number>
         </property>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <widget class="QWidget" name="discoveryPanelContainer" native="true">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>30</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <layout class="QVBoxLayout" name="discoveryPanelLayout">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <property name="spacing">
        <number>0</number>
       </property>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1200</width>
     <height>20</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>