#ifndef THEME_MANAGER_H
#define THEME_MANAGER_H

#include <QObject>
#include <QApplication>
#include <QPalette>
#include <QStyleFactory>
#include <QSettings>

class ThemeManager : public QObject
{
    Q_OBJECT

public:
    enum Theme {
        Light,
        Dark
    };
    Q_ENUM(Theme)

    explicit ThemeManager(QObject *parent = nullptr);
    
    static ThemeManager* instance();
    
    void initialize();
    void setTheme(Theme theme);
    Theme currentTheme() const { return m_currentTheme; }
    
    QString getStyleSheet() const;

signals:
    void themeChanged(Theme theme);

private:
    Theme m_currentTheme;
    static ThemeManager* s_instance;
};

#endif // THEME_MANAGER_H