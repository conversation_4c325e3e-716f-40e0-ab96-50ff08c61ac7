// device_connection_manager.cpp - 修改部分
#include "device_connection_manager.h"
#include <QJsonDocument>
#include <QJsonArray>
#include <QStandardPaths>
#include <QDir>
#include <QCoreApplication>
#include <QDateTime>

DeviceConnectionManager::DeviceConnectionManager(QObject *parent)
    : QObject(parent)
    , m_tcpSocket(new QTcpSocket(this))
    , m_host("")
    , m_port(0)
    , m_connected(false)
    , m_configFile(nullptr)
    , m_expectedFileSize(0)
    , m_receivedBytes(0)
    , m_receivingFile(false)
    , m_receivingConfigData(false)  // 新增初始化
    , m_configTransferFinished(false)  // 新增初始化
{
    // 连接TCP socket信号
    connect(m_tcpSocket, &QTcpSocket::connected, this, &DeviceConnectionManager::onConnected);
    connect(m_tcpSocket, &QTcpSocket::disconnected, this, &DeviceConnectionManager::onDisconnected);
#if QT_VERSION >= QT_VERSION_CHECK(6, 0, 0)
    connect(m_tcpSocket, &QTcpSocket::errorOccurred, this, &DeviceConnectionManager::onError);
#else
    connect(m_tcpSocket, QOverload<QAbstractSocket::SocketError>::of(&QTcpSocket::error),
            this, &DeviceConnectionManager::onError);
#endif
    connect(m_tcpSocket, &QTcpSocket::readyRead, this, &DeviceConnectionManager::onReadyRead);
}

DeviceConnectionManager::~DeviceConnectionManager()
{
    disconnectFromDevice();
    if (m_configFile) {
        delete m_configFile;
    }
}

void DeviceConnectionManager::connectToDevice(const QString &host, quint16 port)
{
    m_host = host;
    m_port = port;

    if (m_tcpSocket->state() == QAbstractSocket::UnconnectedState) {
        m_tcpSocket->connectToHost(host, port);
    }
}

void DeviceConnectionManager::disconnectFromDevice()
{
    if (m_tcpSocket->state() == QAbstractSocket::ConnectedState) {
        m_tcpSocket->disconnectFromHost();
    }
    
    // 清理文件接收状态
    if (m_configFile) {
        if (m_configFile->isOpen()) {
            m_configFile->close();
        }
        m_configFile->deleteLater();
        m_configFile = nullptr;
    }
    m_receivingFile = false;
    m_receivingConfigData = false;  // 重置状态
    m_configTransferFinished = false;  // 重置状态
}

bool DeviceConnectionManager::isConnected() const
{
    return m_connected;
}

void DeviceConnectionManager::sendCommand(const QString &command)
{
    qDebug() << "---------------Sending command:" << command;
    if (m_tcpSocket->state() == QAbstractSocket::ConnectedState) {
        QString fullCommand = command;
        if (!command.endsWith('\n')) {
            fullCommand += '\n';
        }
        m_tcpSocket->write(fullCommand.toUtf8());
    }
}

void DeviceConnectionManager::requestMonitorData(const QStringList &dataTypes, int interval)
{
    qDebug() << "----------------Requesting monitor data for types:" << dataTypes << "with interval:" << interval;
    if (m_tcpSocket->state() != QAbstractSocket::ConnectedState) {
        emit errorOccurred("未连接到设备");
        return;
    }
    
    // 构造监控数据请求报文
    QString request = "REQUEST";
    for (const QString &type : dataTypes) {
        request += "|" + type;
    }
    request += "|" + QString::number(interval);
    request += "\n";
    
    //m_tcpSocket->write(request.toUtf8());
}

void DeviceConnectionManager::startConfigFileReception(qint64 fileSize)
{
    // 创建临时文件用于存储接收到的配置文件
    QString tempDir = QStandardPaths::writableLocation(QStandardPaths::TempLocation);
    qDebug() << "Temporary directory:" << tempDir;
    QString fileName = QString("config_bundle_%1.zip").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss"));
    qDebug() << "Temporary file name:" << fileName;
    m_configFilePath = tempDir + "/" + fileName;
    
    m_configFile = new QFile(m_configFilePath, this);
    if (!m_configFile->open(QIODevice::WriteOnly)) {
        emit errorOccurred(QString("无法创建临时文件: %1").arg(m_configFilePath));
        delete m_configFile;
        m_configFile = nullptr;
        return;
    }
    
    m_expectedFileSize = fileSize;
    m_receivedBytes = 0;
    m_receivingFile = true;
    m_receivingConfigData = true;  // 标记开始接收配置数据
    m_configTransferFinished = false;  // 重置结束标记
    
    emit configFileTransferStarted(fileSize);
}

void DeviceConnectionManager::cancelConfigFileReception()
{
    if (m_receivingFile && m_configFile) {
        m_configFile->close();
        m_configFile->deleteLater();
        m_configFile = nullptr;
        m_receivingFile = false;
        m_receivingConfigData = false;  // 重置状态
        m_configTransferFinished = false;  // 重置状态
        
        // 删除临时文件
        if (!m_configFilePath.isEmpty()) {
            QFile::remove(m_configFilePath);
            m_configFilePath.clear();
        }
    }
}

void DeviceConnectionManager::onConnected()
{
    m_connected = true;
    emit connectionStateChanged(true);
}

void DeviceConnectionManager::onDisconnected()
{
    m_connected = false;
    
    // 清理文件接收状态
    if (m_receivingFile) {
        cancelConfigFileReception();
    }
    
    emit connectionStateChanged(false);
}

void DeviceConnectionManager::onError(QAbstractSocket::SocketError socketError)
{
    Q_UNUSED(socketError);
    m_connected = false;
    
    // 清理文件接收状态
    if (m_receivingFile) {
        cancelConfigFileReception();
    }
    
    emit connectionStateChanged(false);
    emit errorOccurred(m_tcpSocket->errorString());
}

void DeviceConnectionManager::onReadyRead()
{
    if (m_receivingConfigData) {
        // 处理配置文件数据接收
        while (m_tcpSocket->bytesAvailable() > 0 && !m_configTransferFinished) {
            // 检查是否已经接收完所有数据
            if (m_receivedBytes >= m_expectedFileSize) {
                // 继续读取可能的结束标记
                QByteArray data = m_tcpSocket->readAll();
                m_buffer.append(data);
                
                // 检查是否包含结束标记
                int endMarkerPos = m_buffer.indexOf("CONFIG_BUNDLE_END\n");
                if (endMarkerPos != -1) {
                    // 找到结束标记，完成传输
                    m_configTransferFinished = true;
                    
                    if (m_configFile) {
                        m_configFile->close();
                    }
                    
                    m_receivingFile = false;
                    m_receivingConfigData = false;
                    emit configFileReceived(m_configFilePath);
                    
                    // 重置文件相关变量
                    if (m_configFile) {
                        m_configFile->deleteLater();
                        m_configFile = nullptr;
                    }
                    m_configFilePath.clear();
                    m_expectedFileSize = 0;
                    m_receivedBytes = 0;
                    
                    // 保留结束标记之后的数据到缓冲区
                    m_buffer = m_buffer.mid(endMarkerPos + 19); // 19是"CONFIG_BUNDLE_END\n"的长度
                    break;
                } else {
                    // 没有找到结束标记，可能是数据还在传输中
                    // 将数据放回缓冲区，等待更多数据
                    m_buffer = m_buffer.mid(m_buffer.size() - data.size()); // 保持原有数据
                    break;
                }
            }
            
            // 计算还需要读取多少字节
            qint64 bytesToRead = qMin(m_tcpSocket->bytesAvailable(), m_expectedFileSize - m_receivedBytes);
            if (bytesToRead <= 0) {
                break;
            }
            
            QByteArray data = m_tcpSocket->read(bytesToRead);
            if (m_configFile) {
                m_configFile->write(data);
            }
            
            m_receivedBytes += data.size();
            emit configFileTransferProgress(m_receivedBytes, m_expectedFileSize);
        }
    } else {
        // 处理普通文本消息
        m_buffer.append(m_tcpSocket->readAll());
        
        // 处理完整的行
        while (m_buffer.contains('\n')) {
            int lineEnd = m_buffer.indexOf('\n');
            QByteArray line = m_buffer.left(lineEnd);
            m_buffer.remove(0, lineEnd + 1);
            
            parseIncomingData(line);
        }
    }
}

void DeviceConnectionManager::parseIncomingData(const QByteArray &data)
{
    QString dataStr = QString::fromUtf8(data).trimmed();
    
    if (dataStr.startsWith("CONFIG_LIST|")) {
        // 处理配置列表响应
        QStringList parts = dataStr.mid(12).split(",");
        emit configListReceived(parts);
    } 
    else if (dataStr.startsWith("CONFIG_DETAILS|")) {
        // 处理配置详情响应
        QString details = dataStr.mid(15);
        QJsonDocument doc = QJsonDocument::fromJson(details.toUtf8());
        if (doc.isArray()) {
            emit configDetailsReceived(doc.array());
        }
    } 
    else if (dataStr.startsWith("CONFIG_BUNDLE_START|")) {
        qDebug() << "----------开始接收配置文件";
        // 处理配置文件传输开始消息
        bool ok = false;
        qint64 fileSize = dataStr.mid(20).toLongLong(&ok);
        if (ok) {
            startConfigFileReception(fileSize);
            // 新增：发送READY命令给服务端
            sendCommand("READY");
        } else {
            emit errorOccurred("无效的文件大小信息");
        }
    }
    else if (dataStr.startsWith("CONFIG_BUNDLE_END")) {
        // 处理配置文件传输结束消息
        if (m_receivingFile && m_configFile) {
            m_configTransferFinished = true;
            
            m_configFile->close();
            
            m_receivingFile = false;
            m_receivingConfigData = false;
            emit configFileReceived(m_configFilePath);
            
            // 重置文件相关变量
            if (m_configFile) {
                m_configFile->deleteLater();
                m_configFile = nullptr;
            }
            m_configFilePath.clear();
            m_expectedFileSize = 0;
            m_receivedBytes = 0;
        }
    }
    else if (dataStr.startsWith("DATA|")) {
        // 处理监控数据响应
        QStringList parts = dataStr.mid(5).split("|");
        QJsonObject newData;
        
        for (const QString &part : parts) {
            QStringList keyValue = part.split(":");
            if (keyValue.size() == 2) {
                QString key = keyValue[0];
                int value = keyValue[1].toInt();
                newData[key] = value;
            }
        }
        emit monitorDataReceived(newData);
    }
    else if (dataStr.startsWith("ERROR|")) {
        // 处理错误响应
        QString errorMsg = dataStr.mid(6);
        emit errorOccurred(errorMsg);
    }
}