// ws-monitorpanel/plotwidget.cpp
#include "plotwidget.h"
#include <QPainter>
#include <QPaintEvent>
#include <QDateTime>
#include <cmath>
#include <QPainterPath>

PlotWidget::PlotWidget(QWidget *parent)
    : QWidget(parent)
    , m_dataType("cpu")
    , m_unit("%")
    , m_maxDataPoints(2000)
    , m_timeWindow(20000)
    , m_lastUpdateTime(QDateTime::currentMSecsSinceEpoch())
    , m_currentMinY(0)
    , m_currentMaxY(100)
    , m_rangeInitialized(false)
{
    setMinimumSize(200, 100);
    setStyleSheet("background-color: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 4px;");
    
    // 根据数据类型设置颜色和单位
    updateDataTypeSettings();
}


PlotWidget::~PlotWidget()
{
}

void PlotWidget::setDataType(const QString& type)
{
    m_dataType = type;
    updateDataTypeSettings();
    update();
}

void PlotWidget::addDataPoint(double value, qint64 timestamp)
{
    // 添加数据点和时间戳
    m_dataPoints.enqueue(value);
    m_timestamps.enqueue(timestamp);
    
    // 移除超出时间窗口的数据点
    while (!m_timestamps.isEmpty() && 
           (timestamp - m_timestamps.first()) > m_timeWindow) {
        m_dataPoints.dequeue();
        m_timestamps.dequeue();
    }
    
    // 保持最大数据点数限制
    while (m_dataPoints.size() > m_maxDataPoints) {
        m_dataPoints.dequeue();
        m_timestamps.dequeue();
    }
    
    update();
}

void PlotWidget::updatePlotData(const QJsonObject &data)
{
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    
    // 根据数据类型更新曲线
    if (m_dataType == "cpu" && data.contains("cpu")) {
        addDataPoint(data["cpu"].toDouble(), currentTime);
    } else if (m_dataType == "memory" && data.contains("memory")) {
        addDataPoint(data["memory"].toDouble(), currentTime);
    } else if (m_dataType == "disk" && data.contains("disk")) {
        addDataPoint(data["disk"].toDouble(), currentTime);
    } else if (m_dataType == "network" && data.contains("network")) {
        addDataPoint(data["network"].toDouble(), currentTime);
    }
}

void PlotWidget::clearData()
{
    m_dataPoints.clear();
    m_timestamps.clear();
    m_rangeInitialized = false;
    update();
}

void PlotWidget::calculateYRange(double& minY, double& maxY) const
{
    if (m_dataPoints.isEmpty()) {
        minY = 0;
        maxY = 100;
        return;
    }
    
    // 计算当前数据的实际范围
    double actualMin = m_dataPoints.first();
    double actualMax = m_dataPoints.first();
    
    for (double value : m_dataPoints) {
        if (value < actualMin) actualMin = value;
        if (value > actualMax) actualMax = value;
    }
    
    // 如果范围很小，适当扩展以避免图形过于扁平
    double range = actualMax - actualMin;
    if (range < 1.0) { // 范围小于1时扩展
        double center = (actualMin + actualMax) / 2.0;
        actualMin = center - 0.5;
        actualMax = center + 0.5;
        range = 1.0;
    }
    
    // 添加一些边距使图形不贴边
    double margin = range * 0.05; // 5%的边距
    actualMin -= margin;
    actualMax += margin;
    
    // 确保不会出现负值（对于百分比数据）
    if (actualMin < 0) actualMin = 0;
    if (actualMax > 100) actualMax = 100;
    
    // 平滑过渡到新的范围，避免剧烈跳动
    if (!m_rangeInitialized) {
        m_currentMinY = actualMin;
        m_currentMaxY = actualMax;
        m_rangeInitialized = true;
    } else {
        // 使用平滑因子使范围变化更平滑
        const double smoothFactor = 0.1;
        m_currentMinY = m_currentMinY * (1 - smoothFactor) + actualMin * smoothFactor;
        m_currentMaxY = m_currentMaxY * (1 - smoothFactor) + actualMax * smoothFactor;
    }
    
    minY = m_currentMinY;
    maxY = m_currentMaxY;
}

QString PlotWidget::formatYLabel(double value) const
{
    // 根据数据类型和数值大小选择合适的格式
    if (m_dataType == "cpu" || m_dataType == "memory" || m_dataType == "disk" || m_dataType == "network") {
        if (m_dataType == "memory") {
            // 内存以MB为单位，可能需要转换为GB
            if (value >= 1024) {
                return QString::number(value / 1024.0, 'f', 1) + "GB";
            } else {
                return QString::number(value, 'f', 0) + "MB";
            }
        } else if (m_dataType == "disk") {
            // 磁盘以GB为单位，可能需要转换为TB
            if (value >= 1024) {
                return QString::number(value / 1024.0, 'f', 1) + "TB";
            } else {
                return QString::number(value, 'f', 0) + "GB";
            }
        } else if (m_dataType == "network") {
            // 网络以KB/s为单位，可能需要转换为MB/s
            if (value >= 1024) {
                return QString::number(value / 1024.0, 'f', 1) + "MB/s";
            } else {
                return QString::number(value, 'f', 0) + "KB/s";
            }
        } else {
            // CPU使用百分比
            return QString::number(static_cast<int>(value)) + "%";
        }
    } else {
        // 默认格式
        if (value >= 100) {
            return QString::number(static_cast<int>(value));
        } else if (value >= 10) {
            return QString::number(value, 'f', 1);
        } else {
            return QString::number(value, 'f', 2);
        }
    }
}

void PlotWidget::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing, true);
    
    // 绘制背景
    painter.fillRect(rect(), palette().base());
    
    // 计算Y轴范围
    double minY, maxY;
    calculateYRange(minY, maxY);
    double yRange = maxY - minY;
    
    // 绘制网格
    painter.setPen(QPen(QColor(220, 220, 220), 1));
    int gridStepX = width() / 10;
    int gridStepY = height() / 5;
    
    for (int x = 0; x <= width(); x += gridStepX) {
        painter.drawLine(x, 0, x, height());
    }
    
    for (int y = 0; y <= height(); y += gridStepY) {
        painter.drawLine(0, y, width(), y);
    }
    
    // 绘制坐标轴标签
    painter.setPen(QPen(Qt::black, 1));
    painter.setFont(QFont("Arial", 8));
    
    // 绘制Y轴标签
    for (int i = 0; i <= 5; ++i) {
        int y = i * gridStepY;
        double value = maxY - (static_cast<double>(i) / 5.0) * yRange;
        QString label = formatYLabel(value);
        painter.drawText(5, y + 10, label);
    }
    
    // 绘制时间轴标签
    if (!m_timestamps.isEmpty()) {
        qint64 latestTime = m_timestamps.last();
        
        // 绘制时间标签 (0s, 5s, 10s, 15s, 20s)
        for (int i = 0; i <= 4; ++i) {
            qint64 timePoint = latestTime - i * 5000;
            int x = width() - (i * width() / 4);
            QString timeLabel = QString("-%1s").arg(i * 5);
            painter.drawText(x - 15, height() - 5, timeLabel);
        }
    }
    
    // 绘制数据曲线
    if (m_dataPoints.size() > 1 && !m_timestamps.isEmpty() && yRange > 0) {
        QPainterPath path;
        
        qint64 latestTime = m_timestamps.last();
        qint64 timeWindow = m_timeWindow;
        
        bool firstPoint = true;
        qreal lastX = -1, lastY = -1;
        
        for (int i = 0; i < m_dataPoints.size(); ++i) {
            qint64 timestamp = m_timestamps.at(i);
            double value = m_dataPoints.at(i);
            
            qreal x = width() - (width() * (latestTime - timestamp) / timeWindow);
            qreal y = height() - ((value - minY) / yRange) * height();
            
            if (firstPoint) {
                path.moveTo(x, y);
                firstPoint = false;
            } else {
                if (lastX != -1 && lastY != -1) {
                    path.lineTo(x, y);
                }
            }
            
            lastX = x;
            lastY = y;
        }
        
        painter.setPen(QPen(m_plotColor, 2));
        painter.drawPath(path);
    }
    
    // 绘制数据类型标签
    painter.setPen(QPen(m_plotColor, 2));
    painter.setFont(QFont("Arial", 10, QFont::Bold));
    painter.drawText(10, 20, m_dataType);
    
    // 绘制当前值（使用格式化后的值）
    if (!m_dataPoints.isEmpty()) {
        QString valueText = formatYLabel(m_dataPoints.last());
        painter.drawText(10, 35, valueText);
    }
}

void PlotWidget::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    update();
}

void PlotWidget::updateDataTypeSettings()
{
    if (m_dataType == "cpu") {
        m_plotColor = Qt::red;
        m_unit = "%";
    } else if (m_dataType == "memory") {
        m_plotColor = Qt::blue;
        m_unit = "MB";
    } else if (m_dataType == "disk") {
        m_plotColor = Qt::green;
        m_unit = "GB";
    } else if (m_dataType == "network") {
        m_plotColor = QColor(255, 165, 0);
        m_unit = "KB/s";
    } else {
        m_plotColor = Qt::black;
        m_unit = "";
    }
}
