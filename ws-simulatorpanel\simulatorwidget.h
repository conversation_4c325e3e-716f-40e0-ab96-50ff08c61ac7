#ifndef SIMULATORWIDGET_H
#define SIMULATORWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QScrollArea>
#include <QPlainTextEdit>
#include <QSplitter>
#include <QCheckBox>
#include <QLineEdit>
#include <QTabWidget>
#include "Simulator.h"
class SimulatorCard;

class SimulatorWidget : public QWidget
{
    Q_OBJECT

public:
    explicit SimulatorWidget(const QString& deviceId,const QString&ip,QWidget *parent = nullptr);
    ~SimulatorWidget();
    void saveSimulatorConfig();
    void loadSimulatorConfig();

protected:
    void contextMenuEvent(QContextMenuEvent *event) override;

private slots:
    void addTCPSimulator();
    void addUDPSimulator();
    void removeSimulator(SimulatorCard *card);
    void editSimulator(SimulatorCard *card);
    void startSimulator(SimulatorCard *card);
    void stopSimulator(SimulatorCard *card);
    void showSimulatorLog(SimulatorCard *card);
    void appendLogMessage(const QString &message);
    void clearLogMessages();
    void filterLogMessages();
    void handleSimulatorDestroyed(QObject *obj);
    void addTCPServerSimulator(); 

protected:
    void resizeEvent(QResizeEvent *event) override;

private:
    void setupUI();
    void createSimulatorContextMenu();
    void updateLayout();
    void createSimulatorLogTab(Simulator *simulator);
    void removeSimulatorLogTab(Simulator *simulator);
    
    QList<Simulator*> m_simulators;
    QList<SimulatorCard*> m_simulatorCards;
    
    QVBoxLayout *m_mainLayout;
    QSplitter *m_mainSplitter;
    QScrollArea *m_scrollArea;
    QWidget *m_cardsContainer;
    QGridLayout *m_cardsLayout;
    
    // 日志显示相关控件
    QTabWidget *m_logTabWidget;
    QMap<Simulator*, QPlainTextEdit*> m_simulatorLogEditors;
    QLineEdit *m_logFilterEdit;
    QCheckBox *m_autoScrollCheckBox;
    
    QMenu *m_contextMenu;
    QMap<Simulator*, QStringList> m_simulatorLogs;
    QString m_targetDeviceIP;
    QString m_deviceId;
};

#endif // SIMULATORWIDGET_H
