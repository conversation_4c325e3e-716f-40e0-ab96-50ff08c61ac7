#ifndef DEVICEINFO_H
#define DEVICEINFO_H

#include <QString>
#include <QList>

// deviceinfo.h
struct DeviceInfo {
    QString id;          // 设备ID
    QString name;        // 设备名称
    QString ip;          // IP地址
    int port;            // 端口号
    QString os;          // 操作系统
    QString osVersion;   // 操作系统版本
    bool isOnline;       // 是否在线
    bool isFavorite;     // 是否收藏
    
    // 默认构造函数
    DeviceInfo() : port(0), isOnline(false), isFavorite(false) {}
    
    // 构造函数
    DeviceInfo(QString id, QString name, QString ip, int port, QString os, 
               QString osVersion, bool isOnline, bool isFavorite)
        : id(std::move(id)), name(std::move(name)), ip(std::move(ip)), 
          port(port), os(std::move(os)), osVersion(std::move(osVersion)), 
          isOnline(isOnline), isFavorite(isFavorite) {}
};

struct DiscoveredDevice {
    QString name;
    QString ip;
    qint64 lastSeen; // 上次收到心跳包的时间戳
    int port;
    QString os;
    QString osVersion;
    qint64 timestamp;
    
    // 默认构造函数
    DiscoveredDevice() : lastSeen(0), port(0), timestamp(0) {}
    
    // 构造函数
    DiscoveredDevice(const QString& name, const QString& ip, qint64 lastSeen, 
                    int port, const QString& os, const QString& osVersion, qint64 timestamp)
        : name(name), ip(ip), lastSeen(lastSeen), port(port), os(os), osVersion(osVersion), timestamp(timestamp) {}
};


#endif // DEVICEINFO_H
