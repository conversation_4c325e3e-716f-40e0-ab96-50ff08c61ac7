<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DiscoveryPanel</class>
 <widget class="QWidget" name="DiscoveryPanel">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>120</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="dockWidget" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="styleSheet">
      <string notr="true">QWidget#dockWidget {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
}</string>
     </property>
     <layout class="QVBoxLayout" name="dockLayout">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="headerWidget" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>30</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">QWidget {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 6px 6px 0 0;
}</string>
        </property>
        <layout class="QHBoxLayout" name="headerLayout">
         <property name="leftMargin">
          <number>10</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>10</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <property name="spacing">
          <number>0</number>
         </property>
         <item>
          <widget class="QLabel" name="titleLabel">
           <property name="styleSheet">
            <string notr="true">QLabel {
  color: #202124;
  font-weight: 500;
  font-size: 13px;
}</string>
           </property>
           <property name="text">
            <string>发现的设备</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="headerSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="toggleButton">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>24</width>
             <height>24</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>24</width>
             <height>24</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton {
  border: none;
  background-color: transparent;
  border-radius: 4px;
}
QPushButton:hover {
  background-color: #e8eaed;
}
QPushButton:pressed {
  background-color: #dadce0;
}</string>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="contentWidget" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <layout class="QVBoxLayout" name="contentLayout">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <property name="spacing">
          <number>0</number>
         </property>
         <item>
          <widget class="QScrollArea" name="scrollArea">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>100</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="horizontalScrollBarPolicy">
            <enum>Qt::ScrollBarAsNeeded</enum>
           </property>
           <property name="verticalScrollBarPolicy">
            <enum>Qt::ScrollBarAlwaysOff</enum>
           </property>
           <property name="widgetResizable">
            <bool>false</bool>
           </property>
           <property name="styleSheet">
            <string notr="true">QScrollArea {
  border: none;
  background: transparent;
}
QScrollBar:horizontal {
  border: none;
  background: #f1f3f4;
  height: 8px;
  border-radius: 4px;
  margin: 0px 2px;
}
QScrollBar::handle:horizontal {
  background: #dadce0;
  border-radius: 4px;
  min-width: 20px;
}
QScrollBar::handle:horizontal:hover {
  background: #bdc1c6;
}</string>
           </property>
           <widget class="QWidget" name="resultsContainer">
            <property name="geometry">
             <rect>
              <x>0</x>
              <y>0</y>
              <width>398</width>
              <height>98</height>
             </rect>
            </property>
            <property name="styleSheet">
             <string notr="true">QWidget {
  background-color: #ffffff;
  border: none;
}</string>
            </property>
            <layout class="QHBoxLayout" name="resultsLayout">
             <property name="leftMargin">
              <number>10</number>
             </property>
             <property name="topMargin">
              <number>10</number>
             </property>
             <property name="rightMargin">
              <number>10</number>
             </property>
             <property name="bottomMargin">
              <number>10</number>
             </property>
             <property name="spacing">
              <number>10</number>
             </property>
            </layout>
           </widget>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>