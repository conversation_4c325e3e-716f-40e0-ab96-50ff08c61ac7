// DeviceCard.cpp - 修改部分
#include "DeviceCard.h"
#include <QPainter>
#include <QPixmap>
#include <QIcon>
#include <QApplication>
#include <QDateTime>
#include <QContextMenuEvent>
#include <QFileDialog>
#include <QMessageBox>
#include <QProcess>
#include <QDir>
#include <QHBoxLayout>
#include <QPushButton>
#include <QClipboard>
#include <QApplication>
#include <QKeySequence>

DeviceCard::DeviceCard(const QString& deviceName, const QString& deviceIP, bool isFavorite, QWidget *parent)    
    : QFrame(parent)
    , m_deviceName(deviceName)
    , m_deviceIP(deviceIP)
    , m_isOnline(false)
    , m_lastClickTime(0)
    , m_isFavorite(isFavorite)
    , m_contextMenu(nullptr)
{
    setupUI();
    updateStatusIndicator();
    
    // 调用 setDeviceName 方法设置设备名称和收藏状态
    setDeviceName(deviceName, isFavorite);
    m_ipLabel->setText(deviceIP);
    
    // 创建右键菜单
    m_contextMenu = new DeviceCardMenu(this);
    
    // 连接菜单项信号
    connect(m_contextMenu->deleteAction(), &QAction::triggered, this, &DeviceCard::onDeleteDevice);
    connect(m_contextMenu->compareAction(), &QAction::triggered, this, &DeviceCard::onCompareDevice);
    connect(m_contextMenu->changeIconAction(), &QAction::triggered, this, &DeviceCard::onChangeIcon);
    connect(m_contextMenu->openConfigDirAction(), &QAction::triggered, this, &DeviceCard::onOpenConfigDir);
    connect(m_contextMenu->toggleFavoriteAction(), &QAction::triggered, this, &DeviceCard::onToggleFavorite);
}

void DeviceCard::setFavorite(bool isFavorite)
{
    m_isFavorite = isFavorite;
    setDeviceName(m_deviceName, isFavorite);
    
    // 更新菜单项文本
    if (m_contextMenu) {
        m_contextMenu->toggleFavoriteAction()->setText(isFavorite ? "取消收藏" : "添加到收藏");
    }
}

void DeviceCard::setupUI()
{
    // 设置卡片样式
    setStyleSheet(
        "DeviceCard { "
        "  background-color: #ffffff; "
        "  border: 1px solid #e0e0e0; "
        "  border-radius: 8px; "
        "  padding: 10px; "
        "}"
        "DeviceCard:hover { "
        "  border: 1px solid #4285f4; "
        "  background-color: #f8f9fa; "
        "}"
    );
    setCursor(Qt::PointingHandCursor);
    // 设置固定宽度和高度
    setFixedSize(280, 80); // 固定宽度为280像素，高度80像素
    setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed); // 设置固定大小策略
    
    // 创建主布局
    QHBoxLayout* mainLayout = new QHBoxLayout(this);
    mainLayout->setContentsMargins(12, 8, 12, 8);
    mainLayout->setSpacing(10);
    
    // 创建设备图标
    m_iconLabel = new QLabel();
    m_iconLabel->setFixedSize(32, 32);
    m_iconLabel->setStyleSheet(
        "background-color: #4285f4; "
        "border-radius: 6px; "
        "color: white; "
        "font-weight: bold; "
        "qproperty-alignment: AlignCenter;"
    );
    m_iconLabel->setText("D"); // 设备图标占位符
    
    // 创建状态指示器（小圆圈）
    m_statusIndicator = new QLabel();
    m_statusIndicator->setFixedSize(12, 12);
    m_statusIndicator->setStyleSheet(
        "background-color: #ff9800; "
        "border-radius: 6px;"
    );
    
    // 创建信息布局
    QVBoxLayout* infoLayout = new QVBoxLayout();
    infoLayout->setContentsMargins(0, 0, 0, 0);
    infoLayout->setSpacing(4);
    
    // 设备名称布局（包含收藏标识）
    QHBoxLayout* nameLayout = new QHBoxLayout();
    nameLayout->setContentsMargins(0, 0, 0, 0);
    nameLayout->setSpacing(4);
    
    m_nameLabel = new QLabel();
    m_nameLabel->setStyleSheet(
        "font-weight: bold; "
        "font-size: 13px; "
        "color: #202124;"
    );
    m_nameLabel->setWordWrap(false);
    m_nameLabel->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
    
    m_favoriteLabel = new QLabel();
    m_favoriteLabel->setFixedSize(16, 16);
    m_favoriteLabel->setStyleSheet(
        "font-size: 12px; "
        "color: #ffc107;"
    );
    m_favoriteLabel->setText("★");
    m_favoriteLabel->hide(); // 默认隐藏
    
    nameLayout->addWidget(m_nameLabel);
    nameLayout->addWidget(m_favoriteLabel);
    
    // IP地址
    m_ipLabel = new QLabel();
    m_ipLabel->setStyleSheet(
        "color: #5f6368; "
        "font-size: 12px;"
    );
    m_ipLabel->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
    m_ipLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    m_ipLabel->installEventFilter(this);
    
    infoLayout->addLayout(nameLayout);
    infoLayout->addWidget(m_ipLabel);
    
    // 添加到主布局
    mainLayout->addWidget(m_iconLabel);
    mainLayout->addWidget(m_statusIndicator);
    mainLayout->addLayout(infoLayout);
    mainLayout->addStretch();
}

bool DeviceCard::eventFilter(QObject *obj, QEvent *event)
{
    if (obj == m_ipLabel && (event->type() == QEvent::MouseButtonPress || 
                             event->type() == QEvent::MouseButtonDblClick)) {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);
        if (mouseEvent->button() == Qt::LeftButton) {
            if (event->type() == QEvent::MouseButtonDblClick) {
                emit doubleClicked();
                m_lastClickTime = 0;  // 重置单击时间
            } else {
                // 处理单击事件
                qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
                
                // 如果在双击时间阈值内点击，则认为是双击
                if (currentTime - m_lastClickTime < qApp->doubleClickInterval()) {
                    emit doubleClicked();
                    m_lastClickTime = 0;  // 重置时间，避免重复触发
                } else {
                    m_lastClickTime = currentTime;
                    emit clicked();
                }
            }
            return true; // 事件已处理
        }
    }
    return QFrame::eventFilter(obj, event);
}


void DeviceCard::setOnline(bool isOnline)
{
    if (m_isOnline != isOnline) {
        m_isOnline = isOnline;
        updateStatusIndicator();
    }
}

// 在 DeviceCard.cpp 中添加
void DeviceCard::setDeviceName(const QString& name, bool isFavorite)
{
    m_deviceName = name;
    m_nameLabel->setText(name);
    if (isFavorite) {
        m_favoriteLabel->show();
    } else {
        m_favoriteLabel->hide();
    }
}

void DeviceCard::updateStatusIndicator()
{
    if (m_isOnline) {
        m_statusIndicator->setStyleSheet(
            "background-color: #28a745; "  // 绿色表示在线
            "border-radius: 6px;"
        );
    } else {
        m_statusIndicator->setStyleSheet(
            "background-color: #dc3545; "  // 红色表示离线
            "border-radius: 6px;"
        );
    }
}

void DeviceCard::mouseDoubleClickEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        emit doubleClicked();
        m_lastClickTime = 0;  // 重置单击时间
    }
    QFrame::mouseDoubleClickEvent(event);
}

void DeviceCard::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
        
        // 如果在双击时间阈值内点击，则认为是双击
        if (currentTime - m_lastClickTime < qApp->doubleClickInterval()) {
            emit doubleClicked();
            m_lastClickTime = 0;  // 重置时间，避免重复触发
        } else {
            m_lastClickTime = currentTime;
            emit clicked();
        }
    }
    QFrame::mousePressEvent(event);
}

void DeviceCard::contextMenuEvent(QContextMenuEvent *event)
{
    // 检查右键点击是否发生在IP标签上
    if (m_ipLabel->geometry().contains(event->pos())) {
        // 如果IP标签上有选中的文本，显示标准的文本操作菜单
        if (!m_ipLabel->selectedText().isEmpty()) {
            // 创建包含标准操作的菜单
            QMenu textMenu;
            QAction *copyAction = textMenu.addAction("复制(&C)");
            copyAction->setShortcut(QKeySequence::Copy);
            
            QAction *selectAllAction = textMenu.addAction("全选(&A)");
            selectAllAction->setShortcut(QKeySequence::SelectAll);
            
            // 连接操作到标签的槽函数
            connect(copyAction, &QAction::triggered, [this]() {
                QClipboard *clipboard = QApplication::clipboard();
                clipboard->setText(m_ipLabel->selectedText());
            });
            
            connect(selectAllAction, &QAction::triggered, [this]() {
                m_ipLabel->setSelection(0, m_ipLabel->text().length());
            });
            
            textMenu.exec(event->globalPos());
            return;
        } else {
            // 如果没有选中文本，显示全选操作
            QMenu textMenu;
            QAction *selectAllAction = textMenu.addAction("全选(&A)");
            selectAllAction->setShortcut(QKeySequence::SelectAll);
            
            connect(selectAllAction, &QAction::triggered, [this]() {
                m_ipLabel->setSelection(0, m_ipLabel->text().length());
            });
            
            textMenu.exec(event->globalPos());
            return;
        }
    }
    
    // 更新收藏菜单项文本
    if (m_contextMenu) {
        m_contextMenu->toggleFavoriteAction()->setText(m_isFavorite ? "取消收藏" : "添加到收藏");
        m_contextMenu->exec(event->globalPos());
    }
    QFrame::contextMenuEvent(event);
}

void DeviceCard::onCompareDevice()
{
    QMessageBox::information(this, "比较设备", "比较设备功能尚未实现");
    // 这里可以实现设备比较逻辑
}

void DeviceCard::onChangeIcon()
{
    // 打开文件对话框选择图标
    QString fileName = QFileDialog::getOpenFileName(this,
                                                    tr("选择图标"),
                                                    "",
                                                    tr("Images (*.png *.jpg *.bmp *.ico)"));
    
    if (!fileName.isEmpty()) {
        QPixmap pixmap(fileName);
        if (!pixmap.isNull()) {
            m_iconLabel->setPixmap(pixmap.scaled(32, 32, Qt::KeepAspectRatio, Qt::SmoothTransformation));
        } else {
            QMessageBox::warning(this, "错误", "无法加载所选图像");
        }
    }
}

void DeviceCard::onOpenConfigDir()
{
    // 获取程序运行目录
    QString appDir = QCoreApplication::applicationDirPath();
    
    // 构建设备配置目录路径
    // 路径格式: 程序运行目录/devices/设备ID/
    QString configPath = appDir + "/devices/";
    
    QDir dir(configPath);
    if (!dir.exists()) {
        // 如果目录不存在，尝试创建它
        if (!dir.mkpath(".")) {
            QMessageBox::warning(this, "错误", "无法创建设备配置目录: " + configPath);
            return;
        }
    }
    
    // 打开目录
#ifdef Q_OS_WIN
    QProcess::startDetached("explorer.exe", QStringList() << QDir::toNativeSeparators(configPath));
#elif defined(Q_OS_MAC)
    QProcess::startDetached("open", QStringList() << configPath);
#else
    QProcess::startDetached("xdg-open", QStringList() << configPath);
#endif
}

void DeviceCard::onToggleFavorite()
{
    m_isFavorite = !m_isFavorite;
    setFavorite(m_isFavorite);
    // 可以在这里添加保存收藏状态到配置文件的逻辑
}

void DeviceCard::onDeleteDevice()
{
    // 创建更美观的删除确认对话框
    QMessageBox msgBox;
    msgBox.setWindowTitle("确认删除");
    msgBox.setText(QString("确定要删除设备 \"%1\" 吗？").arg(m_deviceName));
    msgBox.setInformativeText(QString("IP地址: %1\n\n删除后将无法恢复，请谨慎操作。").arg(m_deviceIP));
    msgBox.setStandardButtons(QMessageBox::Yes | QMessageBox::Cancel);
    msgBox.setDefaultButton(QMessageBox::Cancel);
    msgBox.setIcon(QMessageBox::Question);
    
    // 设置样式
    msgBox.setStyleSheet(
        "QMessageBox {"
        "  background-color: #ffffff;"
        "  border: 1px solid #e0e0e0;"
        "  border-radius: 8px;"
        "}"
        "QLabel {"
        "  color: #202124;"
        "  font-size: 13px;"
        "}"
        "QMessageBox QLabel {"
        "  color: #202124;"
        "  font-size: 14px;"
        "  padding: 5px;"
        "}"
        "QMessageBox QLabel#qt_msgbox_informativelabel {"
        "  color: #5f6368;"
        "  font-size: 12px;"
        "  qproperty-alignment: AlignLeft;"
        "  padding: 5px;"
        "}"
        "QPushButton {"
        "  padding: 6px 16px;"
        "  border-radius: 4px;"
        "  font-size: 13px;"
        "  font-weight: 500;"
        "}"
        "QPushButton:default {"
        "  background-color: #4285f4;"
        "  color: white;"
        "  border: 1px solid #4285f4;"
        "  min-width: 70px;"
        "}"
        "QPushButton:hover:default {"
        "  background-color: #3367d6;"
        "}"
        "QPushButton:pressed:default {"
        "  background-color: #2a56c6;"
        "}"
        "QPushButton:cancel {"
        "  background-color: #f8f9fa;"
        "  color: #5f6368;"
        "  border: 1px solid #d9d9d9;"
        "  min-width: 70px;"
        "}"
        "QPushButton:hover:cancel {"
        "  background-color: #e8eaed;"
        "}"
        "QPushButton:pressed:cancel {"
        "  background-color: #dadce0;"
        "}"
    );
    
    // 获取按钮并设置对象名称以便应用样式
    QAbstractButton* yesButton = msgBox.button(QMessageBox::Yes);
    QAbstractButton* cancelButton = msgBox.button(QMessageBox::Cancel);
    
    if (yesButton) {
        yesButton->setText("删除");
        yesButton->setObjectName("deleteButton");
    }
    
    if (cancelButton) {
        cancelButton->setText("取消");
        cancelButton->setObjectName("cancelButton");
    }
    
    int ret = msgBox.exec();
    
    if (ret == QMessageBox::Yes) {
        emit deleteDevice(m_deviceIP);
    }
}
