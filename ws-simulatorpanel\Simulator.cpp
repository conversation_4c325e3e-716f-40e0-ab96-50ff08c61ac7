#include "Simulator.h"

Simulator::Simulator(QObject *parent)
    : QObject(parent)
    , m_targetPort(8080)
    , m_messageInterval(1000)
    , m_timer(new QTimer(this))
    , m_isRunning(false)
    , m_packageCnt(0)
{
}

bool Simulator::isRunning() const
{
    return m_isRunning;
}

Simulator::~Simulator()
{
}

void Simulator::setName(const QString &name)
{
    m_name = name;
}

QString Simulator::name() const
{
    return m_name;
}

void Simulator::setTargetAddress(const QString &address)
{
    m_targetAddress = address;
}

QString Simulator::targetAddress() const
{
    return m_targetAddress;
}

void Simulator::setTargetPort(quint16 port)
{
    m_targetPort = port;
}

quint16 Simulator::targetPort() const
{
    return m_targetPort;
}

void Simulator::setMessageInterval(int interval)
{
    m_messageInterval = interval;
}

int Simulator::messageInterval() const
{
    return m_messageInterval;
}