// network/DiscoveryListener.cpp
#include "DiscoveryListener.h"
#include <QNetworkDatagram>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QDateTime>

const QHostAddress DiscoveryListener::MULTICAST_ADDRESS("*************");
const quint16 DiscoveryListener::LISTEN_PORT = 5001;

DiscoveryListener::DiscoveryListener(QObject *parent)
    : QObject(parent)
    , receiveSocket(new QUdpSocket(this))
{
    connect(receiveSocket, &QUdpSocket::readyRead, this, &DiscoveryListener::processPendingDatagrams);
}

void DiscoveryListener::start(QNetworkInterface targetNetworkInterface)
{
    m_targetNetworkInterface = targetNetworkInterface;

    if (receiveSocket->bind(QHostAddress::AnyIPv4, LISTEN_PORT, QUdpSocket::ShareAddress | QUdpSocket::ReuseAddressHint)) {
        bool joined = receiveSocket->joinMulticastGroup(MULTICAST_ADDRESS, m_targetNetworkInterface);
        
        if (joined) {
            receiveSocket->setMulticastInterface(m_targetNetworkInterface);
        }
    } else {
        qDebug() << "Failed to bind to port" << LISTEN_PORT << ":" << receiveSocket->errorString();
    }
}

void DiscoveryListener::stop()
{
    if (receiveSocket->state() == QUdpSocket::BoundState) {
        receiveSocket->leaveMulticastGroup(MULTICAST_ADDRESS);
    }
    receiveSocket->close();
}

void DiscoveryListener::processPendingDatagrams()
{
    while (receiveSocket->hasPendingDatagrams()) {
        QNetworkDatagram datagram = receiveSocket->receiveDatagram();
        QString data = QString::fromUtf8(datagram.data());

        // 解析JSON格式的数据
        QJsonDocument doc = QJsonDocument::fromJson(data.toUtf8());
        
        if (!doc.isNull() && doc.isObject()) {
            QJsonObject obj = doc.object();
            
            // 检查type字段是否为AGENT_BEACON
            if (obj["type"].toString() == "AGENT_BEACON") {
                // 提取所有必要信息
                DiscoveredDevice device;
                device.name = obj["hostname"].toString();
                device.ip = obj["ip"].toString();
                device.port = obj["port"].toInt();
                device.os = obj["os"].toString();
                device.osVersion = obj["os_version"].toString();
                device.timestamp = obj["timestamp"].toVariant().toLongLong();
                device.lastSeen = QDateTime::currentMSecsSinceEpoch();
                
                // 验证IP地址格式
                QHostAddress addr(device.ip);
                if (addr.isNull()) {
                    qDebug() << "Invalid IP address received:" << device.ip;
                    continue;
                }

                emit deviceResponseReceived(device);
            }
        } else {
            qDebug() << "Invalid JSON received:" << data;
        }
    }
}
