// TCPServerSimulator.h
#ifndef TCPSERVERSIMULATOR_H
#define TCPSERVERSIMULATOR_H

#include "Simulator.h"
#include <QTcpServer>
#include <QTcpSocket>
#include <QList>

class TCPServerSimulator : public Simulator
{
    Q_OBJECT
public:
    explicit TCPServerSimulator(QObject *parent = nullptr);
    ~TCPServerSimulator();
    
    void start() override;
    void stop() override;
    Type type() const override;
    QByteArray buildPacket() override;
    
private slots:
    void sendTCPMessage();
    void onNewConnection();
    void onClientDisconnected();
    void onError(QAbstractSocket::SocketError socketError);

private:
    QTcpServer *m_tcpServer;
    QList<QTcpSocket*> m_clients;
};

#endif // TCPSERVERSIMULATOR_H
