#ifndef MONITORSELECTOR_H
#define MONITORSELECTOR_H

#include <QDialog>
#include <QList>
#include <QVBoxLayout>
#include <QCheckBox>
#include <QPushButton>
#include <QFrame>
#include <QLabel>
#include <QScrollArea>

class MonitorItemWidget;

class MonitorSelector : public QDialog
{
    Q_OBJECT

public:
    explicit MonitorSelector(const QList<QString> &availableItems, 
                            const QList<QString> &addedItems,
                            QWidget *parent = nullptr);
    ~MonitorSelector();

    QList<QString> getSelectedItems() const;

private slots:
    void onOkClicked();
    void onCancelClicked();
    void updateOkButtonState();

private:
    void setupUI();
    void populateItems();

    QList<QString> m_availableItems;
    QList<QString> m_addedItems;
    QList<MonitorItemWidget*> m_itemWidgets;
    QPushButton *m_okButton;
    QPushButton *m_cancelButton;
    QVBoxLayout *m_mainLayout;
    QVBoxLayout *m_itemsLayout;
    
    QList<QString> m_selectedItems; // 用户新选择的项目
};

// 单个监控项控件类
class MonitorItemWidget : public QFrame
{
    Q_OBJECT

public:
    explicit MonitorItemWidget(const QString &itemName, bool isAdded, bool isSelected, QWidget *parent = nullptr);

    QString itemName() const;
    bool isChecked() const;
    void setChecked(bool checked);
    bool isAdded() const { return m_isAdded; }

signals:
    void stateChanged(bool checked);
    void clicked();

protected:
    void mousePressEvent(QMouseEvent *event) override;

private slots:
    void onCheckBoxStateChanged(int state);

private:
    void updateStyle();

    QString m_itemName;
    QCheckBox *m_checkBox;
    QLabel *m_itemLabel;
    bool m_isAdded;
    bool m_isSelected;
};

#endif // MONITORSELECTOR_H