#include "monitor_data_client.h"
#include <QJsonDocument>
#include <QJsonArray>
#include <QHostAddress>
#include <QTimerEvent>

MonitorDataClient::MonitorDataClient(QObject *parent)
    : QObject(parent)
    , m_tcpSocket(new QTcpSocket(this))
    , m_host("127.0.0.1")
    , m_port(8080)
    , m_connected(false)
{
    // 连接TCP socket信号
    connect(m_tcpSocket, &QTcpSocket::connected, this, &MonitorDataClient::onConnected, Qt::QueuedConnection);
    connect(m_tcpSocket, &QTcpSocket::disconnected, this, &MonitorDataClient::onDisconnected, Qt::QueuedConnection);
#if QT_VERSION >= QT_VERSION_CHECK(6, 0, 0)
    connect(m_tcpSocket, &QTcpSocket::errorOccurred, this, &MonitorDataClient::onError, Qt::QueuedConnection);
#else
    connect(m_tcpSocket, QOverload<QAbstractSocket::SocketError>::of(&QTcpSocket::error),
            this, &MonitorDataClient::onError, Qt::QueuedConnection);
#endif
    connect(m_tcpSocket, &QTcpSocket::readyRead, this, &MonitorDataClient::onReadyRead, Qt::QueuedConnection);
}

MonitorDataClient::~MonitorDataClient()
{
    disconnectFromServer();
}

void MonitorDataClient::connectToServer(const QString &host, quint16 port)
{
    m_host = host;
    m_port = port;

    qDebug() << "Connecting to" << host << ":" << port;

    if (m_tcpSocket->state() == QAbstractSocket::UnconnectedState) {
        m_tcpSocket->connectToHost(QHostAddress(host), port);
    }
}

void MonitorDataClient::disconnectFromServer()
{
    if (m_tcpSocket->state() == QAbstractSocket::ConnectedState) {
        m_tcpSocket->disconnectFromHost();
    }
}

void MonitorDataClient::requestMonitorData(const QStringList &dataTypes, int interval)
{
    if (m_tcpSocket->state() != QAbstractSocket::ConnectedState) {
        emit errorOccurred("未连接到服务器");
        return;
    }
    
    // 构造请求报文
    // 格式: REQUEST|cpu|memory|disk|network|1000
    QString request = "REQUEST";
    for (const QString &type : dataTypes) {
        request += "|" + type;
    }
    request += "|" + QString::number(interval);
    request += "\n";
    
    m_tcpSocket->write(request.toUtf8());
}

QJsonObject MonitorDataClient::getMonitorData() const
{
    return m_monitorData;
}

void MonitorDataClient::onConnected()
{
    qDebug() << "Connected to" << m_tcpSocket->peerAddress().toString() << ":" << m_tcpSocket->peerPort();
    m_connected = true;
    emit connectionStateChanged(true);
}

void MonitorDataClient::onDisconnected()
{
    qDebug() << "Disconnected from" << m_tcpSocket->peerAddress().toString() << ":" << m_tcpSocket->peerPort();
    m_connected = false;
    emit connectionStateChanged(false);
}

void MonitorDataClient::onReadyRead()
{
    while (m_tcpSocket->canReadLine()) {
        QByteArray line = m_tcpSocket->readLine();
        parseMonitorData(line);
    }
}

void MonitorDataClient::onError(QAbstractSocket::SocketError socketError)
{
    qDebug() << "--------Socket error: " << m_tcpSocket->errorString();
    Q_UNUSED(socketError);
    m_connected = false;
    emit connectionStateChanged(false);
    emit errorOccurred(m_tcpSocket->errorString());
}

void MonitorDataClient::parseMonitorData(const QByteArray &data)
{
    // 解析服务端返回的数据
    // 服务端返回格式为: DATA|cpu:45|memory:67|disk:23|network:15
    QString dataStr = QString::fromUtf8(data).trimmed();
    
    if (dataStr.startsWith("DATA|")) {
        QStringList parts = dataStr.mid(5).split("|"); // 移除 "DATA|" 前缀
        QJsonObject newData;
        
        for (const QString &part : parts) {
            QStringList keyValue = part.split(":");
            if (keyValue.size() == 2) {
                // 根据键名处理不同的数据类型
                QString key = keyValue[0];
                int value = keyValue[1].toInt();
                
                // 对于内存、磁盘和网络数据，可能需要转换单位
                if (key == "memory") {
                    // 假设原始数据是MB，保持原样发送
                    newData[key] = value;
                } else if (key == "disk") {
                    // 假设原始数据是GB，保持原样发送
                    newData[key] = value;
                } else if (key == "network") {
                    // 假设原始数据是KB/s，保持原样发送
                    newData[key] = value;
                } else {
                    // CPU使用率等百分比数据
                    newData[key] = value;
                }
            }
        }

        qDebug() << "-----Received monitor data:" << newData;
        
        m_monitorData = newData;
        emit monitorDataUpdated(m_monitorData);
    } else if (dataStr.startsWith("ERROR|")) {
        QString errorMsg = dataStr.mid(6); // 移除 "ERROR|" 前缀
        emit errorOccurred(errorMsg);
    }
}