#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QListWidgetItem>
#include <QMessageBox>
#include <QRegularExpression>
#include <QSettings>
#include <QNetworkInterface>
#include <QDateTime>
#include "workstation/workstation.h"
#include <QDir>
#include <QJsonObject>
#include <QJsonArray>
#include <QUuid>
#include <QStandardPaths>
#include <QToolBar>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , m_deviceStatusTimer(new QTimer(this))
    , m_discoveredDevicesManager(new DiscoveredDevicesManager(this))
    , m_discoveryPanel(nullptr)
    , m_detailsPanel(nullptr)
    , m_deviceListPanel(nullptr)
{
    ui->setupUi(this);
    setWindowTitle("MonitorBox");
    
    // 初始化主题管理器
    ThemeManager::instance()->initialize();
    
    // 初始化面板
    setupPanels();
    
    // 连接信号槽
    setupConnections();
    
    // 设置主题切换
    // setupThemeToggle();
    
    // 启动设备发现
    m_discoveredDevicesManager->startDiscovery();
    
    // 加载保存的设备
    loadSavedDevices();
    
    // 设置设备状态定时器
    setupDeviceStatusTimer();
    
    resize(1400, 800);
    setMinimumSize(1200, 700);
}

MainWindow::~MainWindow()
{
    delete ui;
}

//发现面板与主面板的通讯：
//1. 获取主面板已添加或已删除的设备，将发现的设备标记上已添加或new!!!
//2. 获取主面板添加或删除某个设备的命令
void MainWindow::setupPanels()
{
    // 创建面板实例
    m_discoveryPanel = new DiscoveryPanel(m_discoveredDevicesManager, this);
    m_detailsPanel = new DeviceDetailsPanel(this);
    m_deviceListPanel = new DeviceListPanel(this);
    
    // 设置面板数据映射
    m_discoveryPanel->setDevicesMap(reinterpret_cast<QMap<QString, void*>*>(&m_deviceMap));
    
    // 添加面板到布局中
    ui->deviceListPanelLayout->addWidget(m_deviceListPanel);
    ui->discoveryPanelLayout->addWidget(m_discoveryPanel);
    ui->detailsPanelLayout->addWidget(m_detailsPanel);
}

void MainWindow::setupConnections()
{
    // 发现面板操作
    connect(m_discoveryPanel, &DiscoveryPanel::addDeviceRequested,
            this, &MainWindow::onAddDeviceRequested);
    // 设备面板的设备卡片操作
    connect(m_deviceListPanel, &DeviceListPanel::deviceCardClicked, 
            this, &MainWindow::onDeviceCardClicked);
    connect(m_deviceListPanel, &DeviceListPanel::deviceCardDoubleClicked, 
            this, &MainWindow::onDeviceCardDoubleClicked);
    connect(m_deviceListPanel, &DeviceListPanel::deviceDeleteRequested,
            this, &MainWindow::onDeleteDeviceRequested);
    // 设备面板空白地方操作
    connect(m_deviceListPanel, &DeviceListPanel::emptyAreaClicked,
            this, &MainWindow::onDeviceListPanelEmptyAreaClicked);
    // 搜索框
    connect(ui->searchLineEdit, &QLineEdit::textChanged, 
            this, &MainWindow::onSearchTextChanged);
}

void MainWindow::onSearchTextChanged(const QString &text)
{
    m_deviceListPanel->filterDevices(text);
}


void MainWindow::onDeviceListPanelEmptyAreaClicked()
{
    m_detailsPanel->clearDeviceDetails();
}

void MainWindow::onDeleteDeviceRequested(const QString& deviceIP)
{
    // 查找要删除的设备
    QString deviceKey;
    DeviceInfo* deviceInfo = nullptr;
    
    for (auto it = m_deviceMap.begin(); it != m_deviceMap.end(); ++it) {
        if (it.value()->ip == deviceIP) {
            deviceKey = it.key();
            deviceInfo = it.value();
            break;
        }
    }
    
    if (!deviceKey.isEmpty() && deviceInfo) {
        // 调用现有的删除函数
        removeDeviceCard(deviceKey);
    }
}

void MainWindow::setupDeviceStatusTimer()
{
    // 每秒检查一次设备状态
    connect(m_deviceStatusTimer, &QTimer::timeout, this, [this]() {
        // 更新所有设备的状态
        for (auto it = m_deviceMap.begin(); it != m_deviceMap.end(); ++it) {
            QString deviceIP = it.value()->ip;
            bool isOnline = m_discoveredDevicesManager->isDeviceOnline(deviceIP);
            
            // 只有状态发生变化时才更新
            if (it.value()->isOnline != isOnline) {
                it.value()->isOnline = isOnline;
                
                // 更新设备列表面板中设备的状态
                m_deviceListPanel->updateDeviceStatus(it.key(), isOnline);
                
                // 如果当前详情面板显示的是这个设备，则更新详情面板
                if (m_detailsPanel->getCurrentDeviceIP() == deviceIP) {
                    m_detailsPanel->showDeviceDetails(it.value());
                }
            }
        }
    });
    
    m_deviceStatusTimer->start(1000);
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    updateSoftwareConfig();
    QMainWindow::closeEvent(event);
}

void MainWindow::removeDeviceCard(const QString& deviceKey)
{
    // 从映射中删除设备信息
    DeviceInfo* device = nullptr;
    if (m_deviceMap.contains(deviceKey)) {
        device = m_deviceMap.take(deviceKey);
    }

    // 从设备列表面板中移除设备
    m_deviceListPanel->removeDevice(deviceKey);

    // 更新软件配置文件
    updateSoftwareConfig();
    
    // 更新发现面板中对应设备的状态为未添加
    if (m_discoveryPanel && device) {
        m_discoveryPanel->updateDeviceAddedStatus(device->ip, false);
    }
    
    // 清除详情面板显示的信息
    m_detailsPanel->clearDeviceDetails();
    
    if (device) {
        QString deviceDir = QCoreApplication::applicationDirPath() + "/devices/" + device->id;
        QDir dir(deviceDir);
        if (dir.exists()) {
            dir.removeRecursively();
        }
    }

    
    // 删除设备信息对象
    if (device) {
        delete device;
    }
}

void MainWindow::updateDeviceCardStatus(const QString& deviceKey, bool isOnline)
{
    // 更新设备信息中的状态
    if (m_deviceMap.contains(deviceKey)) {
        m_deviceMap[deviceKey]->isOnline = isOnline;
        // 更新设备列表面板中设备的状态
        m_deviceListPanel->updateDeviceStatus(deviceKey, isOnline);
    }
}

void MainWindow::loadSavedDevices()
{
    QString configPath = getSoftwareConfigPath();
    QFile configFile(configPath);
    
    // 如果配置文件不存在，则直接返回
    if (!configFile.exists()) {
        return;
    }
    
    if (!configFile.open(QIODevice::ReadOnly)) {
        QMessageBox::warning(this, "错误", "无法读取配置文件: " + configPath);
        return;
    }
    
    QByteArray jsonData = configFile.readAll();
    configFile.close();
    
    QJsonDocument doc = QJsonDocument::fromJson(jsonData);
    if (!doc.isObject()) {
        QMessageBox::warning(this, "错误", "配置文件格式错误");
        return;
    }
    
    QJsonObject config = doc.object();
    if (!config.contains("devices") || !config["devices"].isArray()) {
        return; // 没有设备数据
    }
    
    QJsonArray devicesArray = config["devices"].toArray();

    for (int i = 0; i < devicesArray.size(); ++i) {
        QJsonObject deviceObj = devicesArray[i].toObject();
        QString deviceId = deviceObj["id"].toString();
        QString deviceName = deviceObj["name"].toString();
        QString deviceIP = deviceObj["ip"].toString();
        int port = deviceObj["port"].toInt();
        QString os = deviceObj["os"].toString();
        QString osVersion = deviceObj["osVersion"].toString();
        bool isFavorite = deviceObj["isFavorite"].toBool();
        
        QString deviceKey = deviceId;

        // 创建设备信息结构
        DeviceInfo* device = new DeviceInfo{
            deviceId,
            deviceName,
            deviceIP,
            port,
            os,
            osVersion,
            false, // isOnline，启动时默认离线
            isFavorite
        };
        m_deviceMap[deviceKey] = device;

        // 添加到设备列表面板
        m_deviceListPanel->addDevice(deviceKey, device);
        
        // 确保设备文件夹存在
        createDeviceDirectory(deviceId, deviceIP);
    }
}

void MainWindow::onDeviceCardDoubleClicked(const QString& deviceKey)
{
    if (!m_deviceMap.contains(deviceKey)) {
        return;
    }
    
    DeviceInfo* device = m_deviceMap[deviceKey];
    m_detailsPanel->showDeviceDetails(device);
    
    if (device->isOnline) {
        WorkStation *workStation = new WorkStation(device->name, device->id, device->ip, device->port,this, nullptr);
        workStation->setAttribute(Qt::WA_DeleteOnClose);
        workStation->show();
        // 连接工作台关闭信号，更新详情面板
        connect(workStation, &WorkStation::workstationClosed, this, [this, device]() {
            m_detailsPanel->showDeviceDetails(device);
        });
    } else {
        QMessageBox::information(this, "设备离线", "设备当前不在线，无法连接。");
    }
}

void MainWindow::onDeviceCardClicked(const QString& deviceKey)
{
    if (!m_deviceMap.contains(deviceKey)) {
        return;
    }
    
    DeviceInfo* device = m_deviceMap[deviceKey];
    m_detailsPanel->showDeviceDetails(device);
}


void MainWindow::createDeviceDirectory(const QString& deviceId, const QString& deviceIP)
{
    // 获取程序运行目录
    QString appDir = QCoreApplication::applicationDirPath();
    QString devicesDir = appDir + "/devices";
    QString deviceDir = devicesDir + "/" + deviceId;  // 使用设备ID作为目录名
    
    // 创建设备目录
    QDir dir;
    if (!dir.exists(devicesDir)) {
        dir.mkpath(devicesDir);
    }
    
    if (!dir.exists(deviceDir)) {
        dir.mkpath(deviceDir);
    }
    
    // 创建设备配置子目录
    QString configDir = deviceDir + "/config";
    if (!dir.exists(configDir)) {
        dir.mkpath(configDir);
    }
    
    // 创建设备日志子目录
    QString logDir = deviceDir + "/logs";
    if (!dir.exists(logDir)) {
        dir.mkpath(logDir);
    }
    
    // 创建设备信息文件
    QString deviceInfoFile = deviceDir + "/device_info.json";
    QFile file(deviceInfoFile);
    if (file.open(QIODevice::WriteOnly)) {
        QJsonObject deviceInfo;
        deviceInfo["id"] = deviceId;
        deviceInfo["ip"] = deviceIP;
        deviceInfo["created"] = QDateTime::currentDateTime().toString(Qt::ISODate);
        deviceInfo["lastConnected"] = "";
        deviceInfo["uptime"] = 0;
        
        // 查找设备完整信息
        for (auto it = m_deviceMap.constBegin(); it != m_deviceMap.constEnd(); ++it) {
            if (it.value()->ip == deviceIP) {
                deviceInfo["name"] = it.value()->name;
                deviceInfo["port"] = it.value()->port;
                deviceInfo["os"] = it.value()->os;
                deviceInfo["osVersion"] = it.value()->osVersion;
                break;
            }
        }
        
        QJsonDocument doc(deviceInfo);
        file.write(doc.toJson());
        file.close();
    }
}

QString MainWindow::getSoftwareConfigPath() const
{
    return QCoreApplication::applicationDirPath() + "/monitor.json";
}

void MainWindow::updateSoftwareConfig()
{
    QString configPath = getSoftwareConfigPath();
    QFile configFile(configPath);
    
    // 读取现有配置或创建新配置
    QJsonObject config;
    if (configFile.open(QIODevice::ReadOnly)) {
        QJsonDocument doc = QJsonDocument::fromJson(configFile.readAll());
        config = doc.object();
        configFile.close();
    }
    
    // 更新设备列表
    QJsonArray devicesArray;
    for (auto it = m_deviceMap.constBegin(); it != m_deviceMap.constEnd(); ++it) {
        QJsonObject deviceObj;
        deviceObj["id"] = it.value()->id;
        deviceObj["name"] = it.value()->name;
        deviceObj["ip"] = it.value()->ip;
        deviceObj["port"] = it.value()->port;
        deviceObj["os"] = it.value()->os;
        deviceObj["osVersion"] = it.value()->osVersion;
        deviceObj["isFavorite"] = it.value()->isFavorite;
        devicesArray.append(deviceObj);
    }
    
    config["devices"] = devicesArray;
    config["lastUpdated"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    // 保存配置文件
    if (configFile.open(QIODevice::WriteOnly)) {
        QJsonDocument doc(config);
        configFile.write(doc.toJson());
        configFile.close();
    }
}

void MainWindow::setupThemeToggle()
{
    // 创建自定义主题切换按钮
    m_themeToggleButton = new ThemeToggleButton(this);
    
    // 将按钮添加到工具栏右侧
    QToolBar *toolBar = new QToolBar(this);
    toolBar->addWidget(m_themeToggleButton);
    toolBar->setStyleSheet("QToolBar { border: none; }");
    addToolBar(Qt::RightToolBarArea, toolBar);
    
    // 连接主题变化信号
    connect(ThemeManager::instance(), &ThemeManager::themeChanged, 
            this, &MainWindow::onThemeChanged);
    
    // 初始化按钮状态
    onThemeChanged(ThemeManager::instance()->currentTheme());
}

void MainWindow::onThemeChanged(ThemeManager::Theme theme)
{
    // 主题变化时不需要做额外处理，按钮会自动更新
    Q_UNUSED(theme);
}


// mainwindow.cpp
void MainWindow::onAddDeviceRequested(const DiscoveredDevice &device)
{
    // 检查设备是否已存在
    bool deviceExists = false;
    for (auto it = m_deviceMap.constBegin(); it != m_deviceMap.constEnd(); ++it) {
        DeviceInfo* info = static_cast<DeviceInfo*>(it.value());
        if (info && info->ip == device.ip) {
            deviceExists = true;
            break;
        }
    }
    
    if (!deviceExists) {
        // 生成设备ID
        QString deviceId = QUuid::createUuid().toString(QUuid::WithoutBraces);
        
        // 创建新的 DeviceInfo 对象
        DeviceInfo* newDevice = new DeviceInfo();
        newDevice->id = deviceId;
        newDevice->name = device.name;
        newDevice->ip = device.ip;
        newDevice->port = device.port;
        newDevice->os = device.os;
        newDevice->osVersion = device.osVersion;
        newDevice->isOnline = true; // 新添加的设备默认为在线
        newDevice->isFavorite = false;
        
        // 使用设备ID作为键添加到设备映射
        QString deviceKey = deviceId;
        m_deviceMap[deviceKey] = newDevice;
        
        // 添加到设备列表面板
        m_deviceListPanel->addDevice(deviceKey, newDevice);
        
        // 更新发现面板中设备的状态
        m_discoveryPanel->updateDeviceAddedStatus(device.ip, true);
        
        // 创建设备目录
        createDeviceDirectory(deviceId, device.ip);
        
        // 更新软件配置文件
        updateSoftwareConfig();
        
        qDebug() << "Device added:" << device.name << "(" << device.ip << ")";
    }
}