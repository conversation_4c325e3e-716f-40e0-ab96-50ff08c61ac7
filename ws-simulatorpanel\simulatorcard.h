#ifndef SIMULATORCARD_H
#define SIMULATORCARD_H

#include <QFrame>
#include <QLabel>
#include "Simulator.h"

class SimulatorCard : public QFrame
{
    Q_OBJECT

public:
    explicit SimulatorCard(Simulator *simulator, QWidget *parent = nullptr);
    Simulator* getSimulator() const { return m_simulator; }
    void updateRunningStatus(bool running);

protected:
    void contextMenuEvent(QContextMenuEvent *event) override;

signals:
    void removeRequested(SimulatorCard *card);
    void editRequested(SimulatorCard *card);
    void startRequested(SimulatorCard *card);
    void stopRequested(SimulatorCard *card);
    void logRequested(SimulatorCard *card);

public slots:
    void updateStatus();
    void updateDisplay();

private:
    void setupUI();
    void createContextMenu();
    void connectSimulatorSignals();

    QAction *m_startAction;
    QAction *m_stopAction;
    QAction *m_editAction;
    QAction *m_removeAction;
    QAction *m_logAction;

    Simulator *m_simulator;
    QLabel *m_nameLabel;
    QLabel *m_typeLabel;
    QLabel *m_statusLabel;
    
    QMenu *m_contextMenu;
    
    static QString getTypeString(Simulator::Type type);
    void updateMenuActions();
};

#endif
