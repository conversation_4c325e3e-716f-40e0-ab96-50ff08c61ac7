#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QMap>
#include <QTimer>
#include <QGridLayout>
#include <QNetworkInterface>
#include <QRegularExpression>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include "network/DiscoveredDevicesManager.h"
#include "hp-discoverypanel//DiscoveryPanel.h"
#include "hp-devicedetailspanel/DeviceDetailsPanel.h"
#include "hp-devicelistpanel/devicelistpanel.h"
#include <QListWidgetItem>
#include <QPushButton>
#include <QAction>
#include "theme_manager.h"
#include "theme_toggle_button.h" // 添加这个包含

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    void removeDeviceCard(const QString& deviceKey);
    void onDeviceCardClicked(const QString& deviceKey);
    void onDeviceCardDoubleClicked(const QString& deviceKey);
    void updateDeviceCardStatus(const QString& deviceKey, bool isOnline);
    void onAddDeviceRequested(const DiscoveredDevice &device);
    void onDeleteDeviceRequested(const QString& deviceIP);
    void onDeviceListPanelEmptyAreaClicked();
    void onSearchTextChanged(const QString &text);
    void onThemeChanged(ThemeManager::Theme theme);

private:
    // 画面初始化
    void setupPanels();
    void setupConnections();
    void setupThemeToggle();
    
    //配置管理
    void createDeviceDirectory(const QString& deviceId, const QString& deviceIP);
    void updateSoftwareConfig();
    QString getSoftwareConfigPath() const;
    
    // 设备管理
    void loadSavedDevices();
    void setupDeviceStatusTimer();
    
    QTimer *m_deviceStatusTimer;
    QMap<QString, DeviceInfo*> m_deviceMap;
    
    // 自动发现相关成员
    DiscoveredDevicesManager *m_discoveredDevicesManager;
    // 画面
    Ui::MainWindow *ui;
    // 面板组件
    DiscoveryPanel *m_discoveryPanel;
    DeviceDetailsPanel *m_detailsPanel;
    DeviceListPanel *m_deviceListPanel;
    
    ThemeToggleButton *m_themeToggleButton; // 修改为新的按钮类型
};

#endif // MAINWINDOW_H
