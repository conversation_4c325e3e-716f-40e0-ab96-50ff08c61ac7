// ws-monitorpanel/monitorpanel.h
#ifndef MONITORPANEL_H
#define MONITORPANEL_H

#include <QWidget>
#include <QHBoxLayout>
#include <QScrollArea>
#include <QJsonObject>
#include <QTimer>
#include <QTabWidget>
#include "ws-monitorpanel/addmonitorcard.h"
#include "ws-monitorpanel/monitorcard.h"
#include "ws-monitorpanel/monitorselector.h"
#include "ws-monitorpanel/plotwidget.h"
#include "monitor_data_client.h"
#include <QList>
    
class DeviceConnectionManager;
class MonitorPanel : public QWidget
{
    Q_OBJECT

public:
     explicit MonitorPanel(const QString &deviceId, const QString &deviceIP, const quint16 &devicePort,
                         DeviceConnectionManager *connectionManager, QWidget *parent = nullptr);
    ~MonitorPanel();

    void loadMonitorConfig();
    void saveMonitorConfig();

private slots:
    void onAddMonitorButtonClicked();
    // void onMonitorDataUpdated(const QJsonObject &data);
    // void onConnectionStateChanged(bool connected);
    // void onErrorOccurred(const QString &error);
    void onMonitorDataReceived(const QJsonObject &data);
    void onConnectionStateChanged(bool connected);
    void onErrorOccurred(const QString &error);

    void attemptReconnect();
    void onMonitorCardDeleted(const QString& cardLabel); 

private:
    void setupUI();
    void setupMonitorItems();
    QString getConfigFilePath() const;
    void createMonitorCard(const QString &item);
    void updateMonitorCardValues(const QJsonObject &data);
    void startAutoSubscription();
    QStringList getRequiredDataTypes() const;

    AddMonitorCard *m_addMonitorButton;
    QScrollArea *m_monitorScrollArea;
    QWidget *m_monitorContainer;
    QHBoxLayout *m_monitorLayout;
    
    QList<QString> m_availableMonitorItems;
    QList<QString> m_addedMonitorItems;
    
    // 添加监控数据客户端
    // MonitorDataClient *m_monitorDataClient;
    DeviceConnectionManager *m_connectionManager;
    QString m_targetId;
    QString m_targetIP;
    quint16 m_targetPort;
    QMap<QString, MonitorCard*> m_monitorCards;
    
    // 添加重连定时器
    QTimer *m_reconnectTimer;
    bool m_isConnected;
    
    // 添加曲线图相关组件
    QTabWidget *m_plotTabWidget;
    QMap<QString, PlotWidget*> m_plotWidgets;
};

#endif // MONITORPANEL_H
