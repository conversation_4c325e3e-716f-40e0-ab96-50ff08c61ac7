#include "monitorselector.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QCheckBox>
#include <QPushButton>
#include <QLabel>
#include <QScrollArea>
#include <QFrame>
#include <QMouseEvent>

MonitorSelector::MonitorSelector(const QList<QString> &availableItems,
                               const QList<QString> &addedItems,
                               QWidget *parent)
    : QDialog(parent)
    , m_availableItems(availableItems)
    , m_addedItems(addedItems)
    , m_okButton(new QPushButton("添加"))
    , m_cancelButton(new QPushButton("取消"))
    , m_mainLayout(new QVBoxLayout)
    , m_itemsLayout(new QVBoxLayout)
{
    setWindowTitle("添加监控项");
    setMinimumSize(450, 350);
    setStyleSheet("QDialog { background-color: #f5f5f5; }");
    setupUI();
    populateItems();
    updateOkButtonState();
}

MonitorSelector::~MonitorSelector()
{
}

void MonitorSelector::setupUI()
{
    setLayout(m_mainLayout);
    m_mainLayout->setSpacing(10);
    
    // 标题
    QLabel *titleLabel = new QLabel("选择要添加的监控项");
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(14);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);
    titleLabel->setStyleSheet("color: #333333; padding: 10px 0;");
    m_mainLayout->addWidget(titleLabel);
    
    // 说明文字
    QLabel *descLabel = new QLabel("• 已添加的监控项显示为灰色\n• 点击任意位置可选择/取消选择");
    descLabel->setStyleSheet("color: #666666; font-size: 11px; padding: 0 0 10px 0;");
    m_mainLayout->addWidget(descLabel);
    
    // 滚动区域用于放置监控项
    QScrollArea *scrollArea = new QScrollArea;
    scrollArea->setWidgetResizable(true);
    scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    scrollArea->setStyleSheet(
        "QScrollArea { border: none; background-color: transparent; }"
        "QScrollBar:vertical {"
        "    border: none;"
        "    background: #f0f0f0;"
        "    width: 10px;"
        "    margin: 0px 0px 0px 0px;"
        "}"
        "QScrollBar::handle:vertical {"
        "    background: #c0c0c0;"
        "    border-radius: 5px;"
        "    min-height: 20px;"
        "}"
        "QScrollBar::handle:vertical:hover {"
        "    background: #a0a0a0;"
        "}"
    );
    
    QWidget *itemsContainer = new QWidget;
    itemsContainer->setLayout(m_itemsLayout);
    itemsContainer->setStyleSheet("background-color: white; border-radius: 6px;");
    m_itemsLayout->setSpacing(1);
    m_itemsLayout->setContentsMargins(0, 0, 0, 0);
    
    scrollArea->setWidget(itemsContainer);
    m_mainLayout->addWidget(scrollArea);
    
    // 按钮布局
    QHBoxLayout *buttonLayout = new QHBoxLayout;
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_cancelButton);
    buttonLayout->addWidget(m_okButton);
    buttonLayout->setSpacing(10);
    
    m_mainLayout->addLayout(buttonLayout);
    
    // 设置按钮样式
    m_cancelButton->setStyleSheet(
        "QPushButton {"
        "    background-color: #f0f0f0;"
        "    border: 1px solid #d0d0d0;"
        "    border-radius: 4px;"
        "    padding: 6px 15px;"
        "    color: #333333;"
        "    font-size: 12px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #e0e0e0;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #d0d0d0;"
        "}"
    );
    
    m_okButton->setStyleSheet(
        "QPushButton {"
        "    background-color: #4285f4;"
        "    border: 1px solid #4285f4;"
        "    border-radius: 4px;"
        "    padding: 6px 15px;"
        "    color: white;"
        "    font-size: 12px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #3367d6;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #2a56c6;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #cccccc;"
        "    border: 1px solid #cccccc;"
        "}"
    );
    
    // 连接信号槽
    connect(m_cancelButton, &QPushButton::clicked, this, &MonitorSelector::onCancelClicked);
    connect(m_okButton, &QPushButton::clicked, this, &MonitorSelector::onOkClicked);
}

void MonitorSelector::populateItems()
{
    for (const QString &item : m_availableItems) {
        bool isAdded = m_addedItems.contains(item);
        bool isSelected = false; // 新选择的项目初始为未选中
        
        MonitorItemWidget *itemWidget = new MonitorItemWidget(item, isAdded, isSelected);
        m_itemWidgets.append(itemWidget);
        m_itemsLayout->addWidget(itemWidget);
        
        if (!isAdded) {
            connect(itemWidget, &MonitorItemWidget::stateChanged, 
                    this, &MonitorSelector::updateOkButtonState);
            connect(itemWidget, &MonitorItemWidget::clicked,
                    this, [this, itemWidget]() {
                        if (!itemWidget->isAdded()) {
                            itemWidget->setChecked(!itemWidget->isChecked());
                        }
                    });
        }
    }
    
    m_itemsLayout->addStretch();
}

void MonitorSelector::updateOkButtonState()
{
    bool hasChecked = false;
    for (MonitorItemWidget *widget : m_itemWidgets) {
        if (widget->isChecked()) {
            hasChecked = true;
            break;
        }
    }
    m_okButton->setEnabled(hasChecked);
}

void MonitorSelector::onOkClicked()
{
    // 收集用户选择的项目
    m_selectedItems.clear();
    for (MonitorItemWidget *widget : m_itemWidgets) {
        if (widget->isChecked()) {
            m_selectedItems.append(widget->itemName());
        }
    }
    accept();
}

void MonitorSelector::onCancelClicked()
{
    reject();
}

QList<QString> MonitorSelector::getSelectedItems() const
{
    return m_selectedItems;
}

// MonitorItemWidget 实现
MonitorItemWidget::MonitorItemWidget(const QString &itemName, bool isAdded, bool isSelected, QWidget *parent)
    : QFrame(parent)
    , m_itemName(itemName)
    , m_isAdded(isAdded)
    , m_isSelected(isSelected)
{
    QHBoxLayout *layout = new QHBoxLayout(this);
    layout->setContentsMargins(15, 10, 15, 10);
    
    m_checkBox = new QCheckBox;
    m_checkBox->setChecked(isAdded || isSelected);
    m_checkBox->setEnabled(!isAdded);
    
    m_itemLabel = new QLabel(itemName);
    
    layout->addWidget(m_checkBox);
    layout->addWidget(m_itemLabel);
    layout->addStretch();
    
    updateStyle();
    
    connect(m_checkBox, &QCheckBox::stateChanged, this, &MonitorItemWidget::onCheckBoxStateChanged);
}

QString MonitorItemWidget::itemName() const
{
    return m_itemName;
}

bool MonitorItemWidget::isChecked() const
{
    return m_checkBox->isChecked();
}

void MonitorItemWidget::setChecked(bool checked)
{
    m_checkBox->setChecked(checked);
}

void MonitorItemWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton && !m_isAdded) {
        setChecked(!isChecked());
    }
    QFrame::mousePressEvent(event);
}

void MonitorItemWidget::onCheckBoxStateChanged(int state)
{
    Q_UNUSED(state);
    emit stateChanged(m_checkBox->isChecked());
}

void MonitorItemWidget::updateStyle()
{
    if (m_isAdded) {
        m_itemLabel->setStyleSheet("color: #999999; font-size: 13px;");
        m_checkBox->setChecked(true);
        m_checkBox->setEnabled(false);
        setStyleSheet(
            "MonitorItemWidget {"
            "    background-color: #fafafa;"
            "    border: none;"
            "}"
            "MonitorItemWidget:hover {"
            "    background-color: #f0f0f0;"
            "}"
        );
        setEnabled(false);
    } else {
        m_itemLabel->setStyleSheet("color: #333333; font-size: 13px;");
        setStyleSheet(
            "MonitorItemWidget {"
            "    background-color: white;"
            "    border: none;"
            "}"
            "MonitorItemWidget:hover {"
            "    background-color: #f5f9ff;"
            "}"
        );
        setEnabled(true);
    }
    
    setFrameShape(QFrame::NoFrame);
    setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    setFixedHeight(42);
    setCursor(m_isAdded ? Qt::ArrowCursor : Qt::PointingHandCursor);
}