// device_connection_manager.h - 需要添加的部分
#ifndef DEVICECONNECTIONMANAGER_H
#define DEVICECONNECTIONMANAGER_H

#include <QObject>
#include <QTcpSocket>
#include <QTimer>
#include <QJsonObject>
#include <QMap>
#include <QFile>

// device_connection_manager.h

class DeviceConnectionManager : public QObject
{
    Q_OBJECT

public:
    explicit DeviceConnectionManager(QObject *parent = nullptr);
    ~DeviceConnectionManager();

    // 连接管理
    void connectToDevice(const QString &host, quint16 port);
    void disconnectFromDevice();
    bool isConnected() const;

    // 数据请求
    void sendCommand(const QString &command);
    void requestMonitorData(const QStringList &dataTypes, int interval);

signals:
    // 连接状态信号
    void connectionStateChanged(bool connected);
    void errorOccurred(const QString &error);
    
    // 数据接收信号
    void configListReceived(const QStringList &configList);
    void configDetailsReceived(const QJsonArray &details);
    void monitorDataReceived(const QJsonObject &data);
    
    // 文件传输信号
    void configFileTransferStarted(qint64 fileSize);
    void configFileTransferProgress(qint64 bytesReceived, qint64 totalBytes);
    void configFileReceived(const QString &filePath);

public slots:
    void startConfigFileReception(qint64 fileSize);
    void cancelConfigFileReception();

private slots:
    void onConnected();
    void onDisconnected();
    void onError(QAbstractSocket::SocketError socketError);
    void onReadyRead();

private:
    void parseIncomingData(const QByteArray &data);
    
    QTcpSocket *m_tcpSocket;
    QString m_host;
    quint16 m_port;
    bool m_connected;
    
    // 用于处理粘包问题的缓冲区
    QByteArray m_buffer;
    
    // 文件传输相关成员变量
    QFile *m_configFile;
    qint64 m_expectedFileSize;
    qint64 m_receivedBytes;
    bool m_receivingFile;
    QString m_configFilePath;
    
    // 标记是否正在接收配置文件数据
    bool m_receivingConfigData;
    // 标记是否收到结束标记
    bool m_configTransferFinished;
};

#endif
