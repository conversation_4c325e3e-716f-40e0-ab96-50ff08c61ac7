#include "devicecardmenu.h"
#include <QApplication>

DeviceCardMenu::DeviceCardMenu(QWidget *parent)
    : QMenu(parent)
{
    // 创建菜单项
    m_deleteAction = new QAction("删除设备", this);
    m_compareAction = new QAction("比较设备", this);
    m_changeIconAction = new QAction("更换图标", this);
    m_openConfigDirAction = new QAction("打开配置目录", this);
    m_toggleFavoriteAction = new QAction("添加到收藏", this);
    
    // 设置图标（可选）
    // m_deleteAction->setIcon(QIcon(":/icons/delete.png"));
    // m_compareAction->setIcon(QIcon(":/icons/compare.png"));
    // m_changeIconAction->setIcon(QIcon(":/icons/icon.png"));
    // m_openConfigDirAction->setIcon(QIcon(":/icons/folder.png"));
    // m_toggleFavoriteAction->setIcon(QIcon(":/icons/star.png"));
    
    // 添加菜单项到菜单
    addAction(m_toggleFavoriteAction);
    addAction(m_openConfigDirAction);
    addAction(m_changeIconAction);
    addSeparator();
    addAction(m_compareAction);
    addAction(m_deleteAction);
    
    setupStyles();
}

void DeviceCardMenu::setupStyles()
{
    // 设置菜单样式
    setStyleSheet(R"(
        QMenu {
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 5px 0px;
            font-family: "Microsoft YaHei", sans-serif;
        }
        
        QMenu::item {
            padding: 6px 24px 6px 32px;
            font-size: 13px;
            color: #202124;
        }
        
        QMenu::item:selected {
            background-color: #4285f4;
            color: white;
        }
        
        QMenu::separator {
            height: 1px;
            background-color: #e0e0e0;
            margin: 4px 0px;
        }
        
        QMenu::icon {
            padding-left: 12px;
        }
    )");
}
