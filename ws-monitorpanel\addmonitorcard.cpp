#include "addmonitorcard.h"
#include <QPainter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QEnterEvent>

AddMonitorCard::AddMonitorCard(QWidget *parent)
    : QFrame(parent)
    , m_hovered(false)
    , m_pressed(false)
{
    // 设置卡片样式
    setStyleSheet(
        "AddMonitorCard { "
        "  background-color: #ffffff; "
        "  border: 1px solid #e0e0e0; "
        "  border-radius: 8px; "
        "}"
        "AddMonitorCard:hover { "
        "  border: 1px solid #4285f4; "
        "  background-color: #f8f9fa; "
        "}"
    );
    setCursor(Qt::PointingHandCursor);
    setFixedSize(80, 100);
    
    // 创建布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    mainLayout->setSpacing(5);
    mainLayout->setAlignment(Qt::AlignCenter);
    
    // 创建图标标签
    m_iconLabel = new QLabel();
    m_iconLabel->setAlignment(Qt::AlignCenter);
    m_iconLabel->setPixmap(drawAddIcon(40));
    
    // 创建名称标签
    m_nameLabel = new QLabel("添加");
    m_nameLabel->setAlignment(Qt::AlignCenter);
    m_nameLabel->setStyleSheet("font-size: 9px; color: #5f6368;");
    
    mainLayout->addWidget(m_iconLabel);
    mainLayout->addWidget(m_nameLabel);
}

void AddMonitorCard::enterEvent(QEnterEvent *event)
{
    m_hovered = true;
    updateStyle();
    QFrame::enterEvent(event);
}

void AddMonitorCard::leaveEvent(QEvent *event)
{
    m_hovered = false;
    m_pressed = false;
    updateStyle();
    QFrame::leaveEvent(event);
}

void AddMonitorCard::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_pressed = true;
        updateStyle();
    }
    QFrame::mousePressEvent(event);
}

void AddMonitorCard::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_pressed = false;
        updateStyle();
        
        // 发射点击信号
        if (rect().contains(event->pos())) {
            emit clicked();
        }
    }
    QFrame::mouseReleaseEvent(event);
}

void AddMonitorCard::updateStyle()
{
    if (m_pressed) {
        setStyleSheet(
            "AddMonitorCard { "
            "  background-color: #e8eaed; "
            "  border: 1px solid #4285f4; "
            "  border-radius: 8px; "
            "}"
        );
    } else if (m_hovered) {
        setStyleSheet(
            "AddMonitorCard { "
            "  background-color: #f8f9fa; "
            "  border: 1px solid #4285f4; "
            "  border-radius: 8px; "
            "}"
        );
    } else {
        setStyleSheet(
            "AddMonitorCard { "
            "  background-color: #ffffff; "
            "  border: 1px solid #e0e0e0; "
            "  border-radius: 8px; "
            "}"
            "AddMonitorCard:hover { "
            "  border: 1px solid #4285f4; "
            "  background-color: #f8f9fa; "
            "}"
        );
    }
}

QPixmap AddMonitorCard::drawAddIcon(int size)
{
    QPixmap pixmap(size, size);
    pixmap.fill(Qt::transparent);
    
    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // 绘制加号
    QColor color = m_pressed ? QColor(0, 64, 133) : QColor(0, 123, 255);
    painter.setPen(QPen(color, 3));
    
    int centerX = size / 2;
    int centerY = size / 2;
    
    // 水平线
    painter.drawLine(centerX - 10, centerY, centerX + 10, centerY);
    // 垂直线
    painter.drawLine(centerX, centerY - 10, centerX, centerY + 10);
    
    return pixmap;
}