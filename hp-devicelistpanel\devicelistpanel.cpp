#include "devicelistpanel.h"
#include "ui_devicelistpanel.h"
#include "DeviceCard.h"
#include <QGridLayout>
#include <QMouseEvent>

DeviceListPanel::DeviceListPanel(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::DeviceListPanel)
{
    ui->setupUi(this);
    ui->frame->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    
    // 添加圆角样式以确保一致性
    ui->frame->setStyleSheet(
        "QFrame {"
        "  background-color: #ffffff;"
        "  border: 1px solid #e0e0e0;"
        "  border-radius: 6px;"
        "}"
    );
}

DeviceListPanel::~DeviceListPanel()
{
    delete ui;
}

void DeviceListPanel::updateDeviceFavoriteStatus(const QString& deviceKey, bool isFavorite)
{
    if (m_deviceCards.contains(deviceKey)) {
        m_deviceCards[deviceKey]->setFavorite(isFavorite);
    }
}

void DeviceListPanel::addDevice(const QString& deviceKey, DeviceInfo* device)
{
    if (!device || m_deviceCards.contains(deviceKey))
        return;
    
    // 创建设备卡片，传递收藏状态
    DeviceCard* card = new DeviceCard(device->name, device->ip, device->isFavorite);
    card->setOnline(device->isOnline);
    m_deviceCards[deviceKey] = card;
    
    // 连接信号
    connect(card, &DeviceCard::clicked, this, &DeviceListPanel::onDeviceCardClicked);
    connect(card, &DeviceCard::doubleClicked, this, &DeviceListPanel::onDeviceCardDoubleClicked);
    // 添加删除信号的连接
    connect(card, &DeviceCard::deleteDevice, this, &DeviceListPanel::onDeleteDeviceRequested);
    
    // 添加到网格布局 - 按从左到右、从上到下的顺序排列
    QGridLayout* gridLayout = qobject_cast<QGridLayout*>(ui->devicesLayout);
    if (gridLayout) {
        // 计算应该放置的位置，确保从左上角开始按顺序排列
        int count = gridLayout->count();
        int row = count / 4;  // 每行4个设备卡片
        int col = count % 4;  // 列号(0-3)
        gridLayout->addWidget(card, row, col);
    }
}

void DeviceListPanel::onDeleteDeviceRequested(const QString& deviceIP)
{
    // 发出信号通知主窗口删除设备
    emit deviceDeleteRequested(deviceIP);
}

void DeviceListPanel::removeDevice(const QString& deviceKey)
{
    if (m_deviceCards.contains(deviceKey)) {
        DeviceCard* card = m_deviceCards.take(deviceKey);
        if (card) {
            // 从布局中移除
            QGridLayout* gridLayout = qobject_cast<QGridLayout*>(ui->devicesLayout);
            if (gridLayout) {
                gridLayout->removeWidget(card);
            }
            card->deleteLater();
        }
    }
}

void DeviceListPanel::updateDeviceStatus(const QString& deviceKey, bool isOnline)
{
    if (m_deviceCards.contains(deviceKey)) {
        m_deviceCards[deviceKey]->setOnline(isOnline);
    }
}

void DeviceListPanel::onDeviceCardClicked()
{
    DeviceCard* card = qobject_cast<DeviceCard*>(sender());
    if (card) {
        // 查找设备键值
        for (auto it = m_deviceCards.constBegin(); it != m_deviceCards.constEnd(); ++it) {
            if (it.value() == card) {
                emit deviceCardClicked(it.key());
                break;
            }
        }
    }
}

void DeviceListPanel::onDeviceCardDoubleClicked()
{
    DeviceCard* card = qobject_cast<DeviceCard*>(sender());
    if (card) {
        // 查找设备键值
        for (auto it = m_deviceCards.constBegin(); it != m_deviceCards.constEnd(); ++it) {
            if (it.value() == card) {
                emit deviceCardDoubleClicked(it.key());
                break;
            }
        }
    }
}

void DeviceListPanel::mousePressEvent(QMouseEvent *event)
{
    // 检查点击是否在设备卡片上
    bool clickedOnCard = false;
    for (auto it = m_deviceCards.constBegin(); it != m_deviceCards.constEnd(); ++it) {
        DeviceCard* card = it.value();
        if (card && card->geometry().contains(event->pos())) {
            clickedOnCard = true;
            break;
        }
    }
    
    // 如果点击在空白区域，发出信号
    if (!clickedOnCard) {
        emit emptyAreaClicked();
    }
    
    QWidget::mousePressEvent(event);
}

void DeviceListPanel::filterDevices(const QString &searchText)
{
    for (auto it = m_deviceCards.constBegin(); it != m_deviceCards.constEnd(); ++it) {
        DeviceCard *card = it.value();
        bool matches = searchText.isEmpty() || 
                      card->deviceName().contains(searchText, Qt::CaseInsensitive) ||
                      card->deviceIP().contains(searchText, Qt::CaseInsensitive);
        card->setVisible(matches);
    }
}

QList<DeviceCard*> DeviceListPanel::getAllDeviceCards() const
{
    return m_deviceCards.values();
}
