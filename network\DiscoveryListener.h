// network/DiscoveryListener.h
#ifndef DISCOVERYLISTENER_H
#define DISCOVERYLISTENER_H

#include <QObject>
#include <QUdpSocket>
#include <QNetworkInterface>
#include "utils/deviceinfo.h"

class DiscoveryListener : public QObject
{
    Q_OBJECT
public:
    explicit DiscoveryListener(QObject *parent = nullptr);
    void start(QNetworkInterface targetNetworkInterface);
    void stop();

signals:
    void deviceResponseReceived(const DiscoveredDevice &device);

private slots:
    void processPendingDatagrams();

private:
    QUdpSocket *receiveSocket;
    QNetworkInterface m_targetNetworkInterface;
    static const QHostAddress MULTICAST_ADDRESS;
    static const quint16 LISTEN_PORT;
};

#endif // DISCOVERYLISTENER_H