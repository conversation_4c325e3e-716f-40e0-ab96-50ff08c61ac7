// UDPSimulator.cpp
#include "UDPSimulator.h"
#include <QDateTime>
#include <QDebug>

UDPSimulator::UDPSimulator(QObject *parent)
    : Simulator(parent)
    , m_udpSocket(new QUdpSocket(this))
{
    connect(m_timer, &QTimer::timeout, this, &UDPSimulator::sendUDPMessage);
}

UDPSimulator::~UDPSimulator()
{
    stop();
}

void UDPSimulator::start()
{
    m_timer->start(m_messageInterval);
    m_isRunning = true;
    emit messageSent(QString("1 已启动，目标地址 %2:%3")
                    .arg(m_name)
                    .arg(m_targetAddress)
                    .arg(m_targetPort));
}

void UDPSimulator::stop()
{
    m_timer->stop();
    m_isRunning = false;
    emit messageSent(QString("%1 已停止").arg(m_name));
}

Simulator::Type UDPSimulator::type() const
{
    return UDP;
}

QByteArray UDPSimulator::buildPacket()
{
    QByteArray packet;
    QDataStream stream(&packet, QIODevice::WriteOnly);

    // 设置字节序
    stream.setByteOrder(m_bigEndian ? QDataStream::BigEndian : QDataStream::LittleEndian);

    // 计算数据区大小
    int dataSize = 0;
    for (const PacketField& field : m_packetFields) {
        dataSize += field.size();
    }

    // 报文总长度 (报文头8字节 + 数据区)
    quint16 packetLength = 8 + dataSize;

    // 添加报文头
    stream << m_packetId;    // 报文ID(2字节)
    stream << packetLength;  // 报文长度(2字节)
    stream << m_packageCnt;       // 计数器(4字节)

    m_packageCnt++;

    // 添加数据区
    for (const PacketField& field : m_packetFields) {
        switch (field.type()) {
        case PacketField::BOOL:
            stream << static_cast<quint8>(field.value().toUInt());
            break;
        case PacketField::BYTE:
            stream << static_cast<quint8>(field.value().toUInt());
            break;
        case PacketField::INT:
            stream << static_cast<qint16>(field.value().toInt());
            break;
        case PacketField::DINT:
            stream << static_cast<qint32>(field.value().toInt());
            break;
        case PacketField::WORD:
            stream << static_cast<quint16>(field.value().toUInt());
            break;
        case PacketField::DWORD:
            stream << static_cast<quint32>(field.value().toUInt());
            break;
        case PacketField::REAL:
            float value = field.value().toFloat();
            stream.writeRawData(reinterpret_cast<const char*>(&value), 4);
            break;
        }
    }
    return packet;
}

void UDPSimulator::sendUDPMessage()
{
    // 使用UDP特定的报文构建方法
    QByteArray packet = buildPacket();
    
    qint64 result = m_udpSocket->writeDatagram(packet, QHostAddress(m_targetAddress), m_targetPort);
    if (result != -1) {
        emit messageSent(QString("UDP Sent to %1:%2 (Packet size: %3 bytes)")
                        .arg(m_targetAddress)
                        .arg(m_targetPort)
                        .arg(result));
    } else {
        emit errorOccurred(QString("UDP Error: %1").arg(m_udpSocket->errorString()));
    }
}