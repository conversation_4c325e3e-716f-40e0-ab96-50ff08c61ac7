// TCPSimulator.cpp
#include "TCPSimulator.h"
#include <QDateTime>
#include <QDebug>

TCPSimulator::TCPSimulator(QObject *parent)
    : Simulator(parent)
    , m_tcpSocket(new QTcpSocket(this))
    , m_connected(false)
{
    connect(m_timer, &QTimer::timeout, this, &TCPSimulator::sendTCPMessage);
    connect(m_tcpSocket, &QTcpSocket::connected, this, &TCPSimulator::onConnected);
    connect(m_tcpSocket, &QTcpSocket::disconnected, this, &TCPSimulator::onDisconnected);
#if QT_VERSION >= QT_VERSION_CHECK(6, 0, 0)
    connect(m_tcpSocket, &QTcpSocket::errorOccurred, this, &TCPSimulator::onError);
#else
    connect(m_tcpSocket, QOverload<QAbstractSocket::SocketError>::of(&QTcpSocket::error),
            this, &TCPSimulator::onError);
#endif
}

TCPSimulator::~TCPSimulator()
{
    stop();
}

void TCPSimulator::start()
{
    m_tcpSocket->connectToHost(QHostAddress(m_targetAddress), m_targetPort);
    m_timer->start(m_messageInterval);
    m_isRunning = true;
    emit messageSent(QString("%1 已启动，连接到 %2:%3")
                    .arg(m_name)
                    .arg(m_targetAddress)
                    .arg(m_targetPort));
}

void TCPSimulator::stop()
{
    m_timer->stop();
    m_tcpSocket->disconnectFromHost();
    m_isRunning = false;
    m_connected = false;
    emit messageSent(QString("%1 已停止").arg(m_name));
}

Simulator::Type TCPSimulator::type() const
{
    return TCP;
}

QByteArray TCPSimulator::buildPacket()
{
    QByteArray packet;
    QDataStream stream(&packet, QIODevice::WriteOnly);

    // 设置字节序
    stream.setByteOrder(m_bigEndian ? QDataStream::BigEndian : QDataStream::LittleEndian);

    // 计算数据区大小
    int dataSize = 0;
    for (const PacketField& field : m_packetFields) {
        dataSize += field.size();
    }
    
    // TCP报文头格式: 报文长度(2字节) + 报文ID(2字节) + 计数器(4字节) + 数据区
    quint16 packetLength = 8 + dataSize; // 报文头8字节 + 数据区大小
    
    // 添加TCP报文头 (报文长度 + 报文ID + 计数器)
    stream << packetLength;  // 报文长度(2字节)
    stream << m_packetId;    // 报文ID(2字节)
    stream << m_packageCnt;  // 计数器(4字节)
    
    m_packageCnt++;
    
    // 添加数据区
    for (const PacketField& field : m_packetFields) {
        switch (field.type()) {
        case PacketField::BOOL:
            stream << static_cast<quint8>(field.value().toUInt());
            break;
        case PacketField::BYTE:
            stream << static_cast<quint8>(field.value().toUInt());
            break;
        case PacketField::INT:
            stream << static_cast<qint16>(field.value().toInt());
            break;
        case PacketField::DINT:
            stream << static_cast<qint32>(field.value().toInt());
            break;
        case PacketField::WORD:
            stream << static_cast<quint16>(field.value().toUInt());
            break;
        case PacketField::DWORD:
            stream << static_cast<quint32>(field.value().toUInt());
            break;
        case PacketField::REAL:
            float value = field.value().toFloat();
            stream.writeRawData(reinterpret_cast<const char*>(&value), 4);
            break;
        case PacketField::STRING: {
            QString paddedString = field.getPaddedStringValue();
            QByteArray stringBytes = paddedString.toUtf8();
            // 确保写入指定长度的字节
            if (stringBytes.size() < field.stringLength()) {
                stringBytes.resize(field.stringLength());
            }
            stream.writeRawData(stringBytes.constData(), field.stringLength());
            break;
        }
        }
    }
    return packet;
}

void TCPSimulator::sendTCPMessage()
{
    // 检查套接字是否处于连接状态
    if (m_tcpSocket->state() == QAbstractSocket::ConnectedState) {
        // 使用TCP特定的报文构建方法
        QByteArray packet = buildPacket();
        
        // 发送数据包
        qint64 result = m_tcpSocket->write(packet);
        
        // 检查发送是否成功
        if (result == -1) {
            emit errorOccurred(QString("TCP发送数据失败: %1").arg(m_tcpSocket->errorString()));
        } else {
            emit messageSent(QString("TCP Sent to %1:%2 (Packet size: %3 bytes)")
                            .arg(m_targetAddress)
                            .arg(m_targetPort)
                            .arg(packet.size()));
        }
    } else if (m_isRunning) {
        // 如果模拟器正在运行但连接已断开，停止模拟器
        stop();
        emit errorOccurred(QString("TCP连接已断开，停止模拟器 %1").arg(m_name));
    }
}

void TCPSimulator::onConnected()
{
    m_connected = true;
    emit messageSent(QString("TCP Connected to %1:%2")
                    .arg(m_targetAddress)
                    .arg(m_targetPort));
}

void TCPSimulator::onDisconnected()
{
    m_connected = false;
    // 如果模拟器仍在运行状态，停止它
    if (m_isRunning) {
        stop();
        emit messageSent(QString("TCP Disconnected from %1:%2，模拟器已停止")
                        .arg(m_targetAddress)
                        .arg(m_targetPort));
    } else {
        emit messageSent(QString("TCP Disconnected from %1:%2")
                        .arg(m_targetAddress)
                        .arg(m_targetPort));
    }
}

void TCPSimulator::onError(QAbstractSocket::SocketError socketError)
{
    Q_UNUSED(socketError);
    m_connected = false;
    // 如果模拟器仍在运行状态，停止它
    if (m_isRunning) {
        stop();
        emit errorOccurred(QString("TCP Error: %1，模拟器已停止").arg(m_tcpSocket->errorString()));
    } else {
        emit errorOccurred(QString("TCP Error: %1").arg(m_tcpSocket->errorString()));
    }
}
