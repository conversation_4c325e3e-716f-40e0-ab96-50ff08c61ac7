#include "simulatorcard.h"
#include <QVBoxLayout>
#include <QMenu>
#include <QContextMenuEvent>

SimulatorCard::SimulatorCard(Simulator *simulator, QWidget *parent)
    : Q<PERSON><PERSON>e(parent)
    , m_simulator(simulator)
{
    setupUI();
    createContextMenu();
    connectSimulatorSignals();
    updateStatus();
}

void SimulatorCard::setupUI()
{
    setStyleSheet("SimulatorCard {"
                  "background-color: #f8f9fa;"
                  "border: 1px solid #dee2e6;"
                  "border-radius: 8px;"
                  "margin: 5px;"
                  "}"
                  "SimulatorCard:hover {"
                  "background-color: #e9ecef;"
                  "border: 1px solid #adb5bd;"
                  "}");
    
    setMinimumSize(180, 120);
    setSizePolicy(QSizePolicy::MinimumExpanding, QSizePolicy::Fixed);
    
    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->setContentsMargins(10, 10, 10, 10);
    
    m_nameLabel = new QLabel(m_simulator->name(), this);
    m_nameLabel->setStyleSheet("font-weight: bold; font-size: 14px;");
    
    m_typeLabel = new QLabel(getTypeString(m_simulator->type()), this);
    m_typeLabel->setStyleSheet("color: #6c757d; font-size: 12px;");
    
    m_statusLabel = new QLabel("未启动", this);
    m_statusLabel->setStyleSheet("color: #6c757d; font-size: 12px;");
    
    layout->addWidget(m_nameLabel);
    layout->addWidget(m_typeLabel);
    layout->addStretch();
    layout->addWidget(m_statusLabel);
}

void SimulatorCard::createContextMenu()
{
    m_contextMenu = new QMenu(this);
    
    m_startAction = new QAction("启动", this);
    connect(m_startAction, &QAction::triggered, this,[=]() {
        emit startRequested(this);
    });
    
    m_stopAction = new QAction("停止", this);
    connect(m_stopAction, &QAction::triggered,this, [=]() {
        emit stopRequested(this);
    });
    
    m_editAction = new QAction("编辑", this);
    connect(m_editAction, &QAction::triggered,this, [=]() {
        emit editRequested(this);
    });
    
    m_removeAction = new QAction("删除", this);
    connect(m_removeAction, &QAction::triggered, this,[=]() {
        emit removeRequested(this);
    });
    
    m_logAction = new QAction("查看日志", this);
    connect(m_logAction, &QAction::triggered,this, [=]() {
        emit logRequested(this);
    });
    
    m_contextMenu->addAction(m_startAction);
    m_contextMenu->addAction(m_stopAction);
    m_contextMenu->addSeparator();
    m_contextMenu->addAction(m_editAction);
    m_contextMenu->addAction(m_removeAction);
    m_contextMenu->addSeparator();
    m_contextMenu->addAction(m_logAction);
    
    // 连接菜单即将显示的信号，用于更新菜单项的启用状态
    connect(m_contextMenu, &QMenu::aboutToShow, this, &SimulatorCard::updateMenuActions);
}

void SimulatorCard::updateMenuActions()
{
    // 根据模拟器的运行状态更新菜单项的启用状态
    bool isRunning = m_simulator->isRunning();
    
    // 运行中时不能启动，停止时不能停止
    m_startAction->setEnabled(!isRunning);
    m_stopAction->setEnabled(isRunning);
    
    // 运行中时不能编辑和删除
    m_editAction->setEnabled(!isRunning);
    m_removeAction->setEnabled(!isRunning);
    
    // 日志查看始终可用
    m_logAction->setEnabled(true);
}

void SimulatorCard::updateDisplay()
{
    m_nameLabel->setText(m_simulator->name());
    m_typeLabel->setText(getTypeString(m_simulator->type()));
}

void SimulatorCard::updateStatus()
{
    // 根据模拟器的运行状态更新显示
    if (m_simulator->isRunning()) {
        m_statusLabel->setText("运行中");
        m_statusLabel->setStyleSheet("color: #28a745; font-weight: bold;");
    } else {
        m_statusLabel->setText("已停止");
        m_statusLabel->setStyleSheet("color: #6c757d; font-size: 12px;");
    }
}

void SimulatorCard::connectSimulatorSignals()
{
    // 连接模拟器的信号到更新状态的槽
    connect(m_simulator, &Simulator::messageSent, this, &SimulatorCard::updateStatus);
    connect(m_simulator, &Simulator::errorOccurred, this, &SimulatorCard::updateStatus);
}

void SimulatorCard::contextMenuEvent(QContextMenuEvent *event)
{
    m_contextMenu->exec(event->globalPos());
}

void SimulatorCard::updateRunningStatus(bool running)
{
    if (running) {
        m_statusLabel->setText("运行中");
        m_statusLabel->setStyleSheet("color: #28a745; font-weight: bold;");
    } else {
        m_statusLabel->setText("已停止");
        m_statusLabel->setStyleSheet("color: #6c757d; font-size: 12px;");
    }
}

QString SimulatorCard::getTypeString(Simulator::Type type)
{
    switch (type) {
    case Simulator::TCP:
        return "TCP 模拟器";
    case Simulator::UDP:
        return "UDP 模拟器";
    case Simulator::TCPServer:
        return "TCP服务器模拟器";
    default:
        return "未知类型";
    }
}
