#ifndef DISCOVERYPANEL_H
#define DISCOVERYPANEL_H

#include <QWidget>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include "network/DiscoveredDevicesManager.h"

namespace Ui {
class DiscoveryPanel;
}

class DiscoveredDeviceCard;

class DiscoveryPanel : public QWidget
{
    Q_OBJECT

public:
    explicit DiscoveryPanel(DiscoveredDevicesManager *discoveryManager, QWidget *parent = nullptr);
    ~DiscoveryPanel();

    void setDevicesMap(const QMap<QString, void*> *devicesMap); // 用于检查设备是否已添加

public slots:
    void togglePanel();
    void updateDeviceAddedStatus(const QString &deviceIP, bool isAdded);

signals:
    void addDeviceRequested(const DiscoveredDevice &device);

private slots:
    void onDeviceDiscovered(const DiscoveredDevice &device);
    void onDeviceRemoved(const QString &deviceIP);
    
private:
    void updatePanelTitle();

    Ui::DiscoveryPanel *ui;
    DiscoveredDevicesManager *m_discoveryManager;
    const QMap<QString, void*> *m_devicesMap;

    bool m_isExpanded;
};

#endif // DISCOVERYPANEL_H
