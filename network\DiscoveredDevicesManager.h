// network/DiscoveredDevicesManager.h
#ifndef DISCOVEREDDEVICESMANAGER_H
#define DISCOVEREDDEVICESMANAGER_H

#include <QObject>
#include <QMap>
#include <QTimer>
#include <QNetworkInterface>
#include "network/DiscoveryListener.h"
#include "utils/deviceinfo.h"

class DiscoveredDevicesManager : public QObject
{
    Q_OBJECT
public:
    explicit DiscoveredDevicesManager(QObject *parent = nullptr);
    ~DiscoveredDevicesManager();
    
    void startDiscovery();
    void stopDiscovery();

    // 检查设备是否在线
    bool isDeviceOnline(const QString &deviceIP) const;
    // 获取所有已发现的设备
    QStringList getDiscoveredDevices() const;

signals:
    // 发现面板需要订阅这个信息
    void deviceDiscovered(const DiscoveredDevice &device);
    // 设备被移除时发送信号
    void deviceRemoved(const QString &deviceIP);

private slots:
    void onDeviceResponseReceived(const DiscoveredDevice &device);
    void checkDeviceTimeout();

private:
    // deviceIP -> deviceInfo
    QMap<QString, DiscoveredDevice> m_discoveredDevices;
    // interfaceName -> listener
    QMap<QString, DiscoveryListener*> m_discoveryListeners; 
    QTimer *m_statusCheckTimer;
    // 10秒内未收到心跳认为离线
    static const int DEVICE_TIMEOUT_MS = 10000; 
};

#endif // DISCOVEREDDEVICESMANAGER_H