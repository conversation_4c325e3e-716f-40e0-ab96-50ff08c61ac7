// monitor_data_client.h
#ifndef MONITORDATACLIENT_H
#define MONITORDATACLIENT_H

#include <QObject>
#include <QTcpSocket>
#include <QTimer>
#include <QMap>
#include <QJsonObject>

class MonitorDataClient : public QObject
{
    Q_OBJECT

public:
    explicit MonitorDataClient(QObject *parent = nullptr);
    ~MonitorDataClient();

    // 连接到服务端
    void connectToServer(const QString &host, quint16 port);
    
    // 断开连接
    void disconnectFromServer();
    
    // 请求监控数据
    void requestMonitorData(const QStringList &dataTypes, int interval = 1000);
    
    // 获取当前监控数据
    QJsonObject getMonitorData() const;

signals:
    // 当监控数据更新时发出信号
    void monitorDataUpdated(const QJsonObject &data);
    // 连接状态变化信号
    void connectionStateChanged(bool connected);
    // 错误信号
    void errorOccurred(const QString &error);

private slots:
    void onConnected();
    void onDisconnected();
    void onReadyRead();
    void onError(QAbstractSocket::SocketError socketError);

private:
    void parseMonitorData(const QByteArray &data);
    
    QTcpSocket *m_tcpSocket;
    QString m_host;
    quint16 m_port;
    QJsonObject m_monitorData;
    bool m_connected;
};

#endif // MONITORDATACLIENT_H