// workstation.h - 更新部分
#ifndef WORKSTATION_H
#define WORKSTATION_H

#include <QMainWindow>
#include <QTimer>
#include "ws-simulatorpanel/simulatorwidget.h"
#include "ws-monitorpanel/monitorpanel.h"
#include "configpanel.h"

QT_BEGIN_NAMESPACE
namespace Ui { class WorkStation; }
QT_END_NAMESPACE
class DeviceConnectionManager;
class MainWindow;
class WorkStation : public QMainWindow
{
    Q_OBJECT

public:
    WorkStation(const QString& deviceName, const QString& deviceId, const QString& deviceIP,int devicePort, MainWindow* mainWindow, QWidget *parent = nullptr);
    ~WorkStation();

signals:
    void workstationClosed();

protected:
    void closeEvent(QCloseEvent *event) override;

private:
    void setupUI();
    Ui::WorkStation *ui;
    
    QString m_deviceName;
    QString m_deviceId;
    QString m_deviceIP;
    int m_devicePort;
    MainWindow* m_mainWindow;
    DeviceConnectionManager *m_connectionManager;
    
    SimulatorWidget *m_simulatorWidget;
    MonitorPanel *m_monitorPanel;
    ConfigPanel *m_configPanel;
};

#endif // WORKSTATION_H