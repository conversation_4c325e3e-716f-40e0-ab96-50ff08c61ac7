// TCPSimulator.h
#ifndef TCPSIMULATOR_H
#define TCPSIMULATOR_H

#include "Simulator.h"
#include <QTcpSocket>

class TCPSimulator : public Simulator
{
    Q_OBJECT
public:
    explicit TCPSimulator(QObject *parent = nullptr);
    ~TCPSimulator();
    
    void start() override;
    void stop() override;
    Type type() const override;

    QByteArray buildPacket() override;
    
private slots:
    void sendTCPMessage();
    void onConnected();
    void onDisconnected();
    void onError(QAbstractSocket::SocketError socketError);

private:
    QTcpSocket *m_tcpSocket;
    bool m_connected;  // 添加连接状态跟踪
};

#endif // TCPSIMULATOR_H
