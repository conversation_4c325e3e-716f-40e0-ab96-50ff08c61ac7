#include "monitorutils.h"

namespace MonitorUtils {
    // CPU颜色策略
    QColor cpuColorStrategy(int value) {
        if (value < 50) {
            return Qt::green;
        } else if (value < 80) {
            return QColor(255, 165, 0);  // 橙色
        } else {
            return Qt::red;
        }
    }
    
    // 内存颜色策略
    QColor memoryColorStrategy(int value) {
        if (value < 60) {
            return Qt::green;
        } else if (value < 85) {
            return QColor(255, 165, 0);  // 橙色
        } else {
            return Qt::red;
        }
    }
    
    // 硬盘颜色策略
    QColor diskColorStrategy(int value) {
        if (value < 70) {
            return Qt::green;
        } else if (value < 90) {
            return QColor(255, 165, 0);  // 橙色
        } else {
            return Qt::red;
        }
    }
    
    // 网络颜色策略
    QColor networkColorStrategy(int value) {
        if (value < 60) {
            return Qt::green;
        } else if (value < 80) {
            return QColor(255, 165, 0);  // 橙色
        } else {
            return Qt::red;
        }
    }
    
    // 温度颜色策略
    QColor temperatureColorStrategy(int value) {
        if (value < 80) {
            return Qt::green;
        } else if (value < 90) {
            return QColor(255, 165, 0);  // 橙色
        } else {
            return Qt::red;
        }
    }
    
    // 默认颜色策略
    QColor defaultColorStrategy(int value) {
        if (value < 50) {
            return Qt::green;
        } else if (value < 80) {
            return QColor(255, 165, 0);  // 橙色
        } else {
            return Qt::red;
        }
    }
}