// DiscoveredDeviceCard.h
#ifndef DISCOVEREDDEVICECARD_H
#define DISCOVEREDDEVICECARD_H

#include <QFrame>
#include <QLabel>
#include <QVBoxLayout>
#include <QMouseEvent>
#include "utils/deviceinfo.h"

class DiscoveredDeviceCard : public QFrame
{
    Q_OBJECT

public:
    explicit DiscoveredDeviceCard(const DiscoveredDevice& device, bool isAdded = false, QWidget *parent = nullptr);
    QString deviceName() const { return m_device.name; }
    QString deviceIP() const { return m_device.ip; }
    
    void updateAddedStatus(bool isAdded);

signals:
    void addDeviceClicked(const DiscoveredDevice& device);

protected:
    void enterEvent(QEnterEvent *event) override;
    void leaveEvent(QEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void paintEvent(QPaintEvent *event) override;

private:
    void updateStyle();
    
    DiscoveredDevice m_device;
    
    QLabel* m_nameLabel;
    QLabel* m_ipLabel;
    QLabel* m_newLabel;
    
    bool m_hovered;
    bool m_pressed;
};

#endif // DISCOVEREDDEVICECARD_H