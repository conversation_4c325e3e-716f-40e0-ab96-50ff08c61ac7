// configpanel.h
#ifndef CONFIGPANEL_H
#define CONFIGPANEL_H

#include <QWidget>
#include <QJsonObject>
#include <QTreeWidgetItem>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QTextEdit>
#include <QSplitter>
#include <QJsonArray>
#include <QProgressDialog>
#include <QDir>
#include <QTextEdit>
#include <QSplitter>

class QTreeWidget;
class QTextBrowser;
class QPushButton;
class DeviceConnectionManager;
class QProgressDialog;
class QFileContentWindow; // 新增文件内容窗口类声明

class ConfigPanel : public QWidget
{
    Q_OBJECT

public:
    explicit ConfigPanel(const QString& deviceId, const QString& deviceIP, int devicePort, 
                        DeviceConnectionManager *connectionManager, QWidget *parent = nullptr);
    ~ConfigPanel();

private slots:
    void onConfigBundleReceived(const QJsonArray &details);
    void onConnectionStateChanged(bool connected);
    void onErrorOccurred(const QString &error);
    void onDownloadConfig();
    void onConfigFileItemClicked(QTreeWidgetItem *item, int column);
    void onConfigFileItemDoubleClicked(QTreeWidgetItem *item, int column);
    void onConfigFileItemRightClicked(const QPoint &pos);
    
    // 文件传输相关槽函数
    void onConfigFileTransferStarted(qint64 fileSize);
    void onConfigFileTransferProgress(qint64 bytesReceived, qint64 totalBytes);
    void onConfigFileReceived(const QString &filePath);
    void onBundleFileItemClicked(QTreeWidgetItem *item, int column);
    void loadLocalConfigFiles();
    // void onFileContentWindowClosed();
    
private:
    void setupUI();
    void extractAndLoadConfigBundle(const QString &zipFilePath);
    void displayConfigBundleContents(const QJsonObject &bundleInfo);
    void extractAndDisplayConfigFiles(const QString &zipFilePath);
    void displayConfigFileList(const QStringList &fileList);
    
    // 新增方法
    void displayBundleFileTree(const QString &bundlePath);
    void populateTreeFromDirectory(const QString &dirPath, QTreeWidgetItem *parentItem = nullptr);
    bool isTextFile(const QByteArray &data);
    // void showFileContent(const QString &content, const QString &fileName); // 新增显示文件内容方法
    // UI组件
    QTreeWidget *m_configFileTreeWidget;
    QPushButton *m_downloadButton;
    QTreeWidget *m_fileListTreeWidget;
    QTextEdit *m_fileContentDisplay;
    QSplitter *m_mainVerticalSplitter;
    QString m_deviceId;
    QString m_deviceIP;
    int m_devicePort;
    
    DeviceConnectionManager *m_connectionManager;
    
    // 存储当前配置数据
    QJsonArray m_currentConfigDetails;
    
    // 进度对话框
    QProgressDialog *m_progressDialog;
    
    // 保存解压后的配置文件目录
    QString m_extractedConfigDir;
    
    // 新增成员变量
    QFileContentWindow *m_fileContentWindow; // 文件内容窗口指针
};

// 新增文件内容窗口类
class QFileContentWindow : public QWidget
{
    Q_OBJECT

public:
    explicit QFileContentWindow(QWidget *parent = nullptr);
    void setContent(const QString &content, const QString &fileName);

protected:
    void closeEvent(QCloseEvent *event) override;

signals:
    void windowClosed();

private:
    QTextEdit *m_contentDisplay;
    QString m_fileName;
};

#endif // CONFIGPANEL_H
