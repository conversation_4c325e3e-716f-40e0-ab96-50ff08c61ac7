#ifndef THEME_TOGGLE_BUTTON_H
#define THEME_TOGGLE_BUTTON_H

#include <QPushButton>
#include <QPainter>
#include <QMouseEvent>
#include "theme_manager.h"

class ThemeToggleButton : public QPushButton
{
    Q_OBJECT

public:
    explicit ThemeToggleButton(QWidget *parent = nullptr);

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;

private slots:
    void onThemeChanged(ThemeManager::Theme theme);

private:
    ThemeManager::Theme m_currentTheme;
    void updateToolTip();
};

#endif // THEME_TOGGLE_BUTTON_H