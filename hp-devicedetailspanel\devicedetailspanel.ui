<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DeviceDetailsPanel</class>
 <widget class="QFrame" name="DeviceDetailsPanel">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>320</width>
    <height>450</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>320</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>320</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>设备详情</string>
  </property>
  <property name="frameShape">
   <enum>QFrame::StyledPanel</enum>
  </property>
  <property name="frameShadow">
   <enum>QFrame::Raised</enum>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <property name="spacing">
    <number>15</number>
   </property>
   <property name="leftMargin">
    <number>20</number>
   </property>
   <property name="topMargin">
    <number>20</number>
   </property>
   <property name="rightMargin">
    <number>20</number>
   </property>
   <property name="bottomMargin">
    <number>20</number>
   </property>
   <item>
    <widget class="QLabel" name="iconLabel">
     <property name="minimumSize">
      <size>
       <width>64</width>
       <height>64</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>64</width>
       <height>64</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
  background-color: #4285f4;
  border-radius: 32px;
  color: white;
  font-size: 24px;
  font-weight: bold;
  qproperty-alignment: AlignCenter;
}</string>
     </property>
     <property name="text">
      <string>D</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="titleLabel">
     <property name="font">
      <font>
       <pointsize>16</pointsize>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
  color: #202124;
  font-weight: 600;
}</string>
     </property>
     <property name="text">
      <string>设备详情</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="Line" name="line">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">QFrame {
  background-color: #e0e0e0;
  height: 1px;
}</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="contentLabel">
     <property name="styleSheet">
      <string notr="true">QLabel {
  color: #5f6368;
  font-size: 13px;
  line-height: 1.5;
}</string>
     </property>
     <property name="text">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;div style='text-align: center; margin: 20px;'&gt;&lt;h3 style='color: #5f6368; margin-bottom: 15px;'&gt;请选择设备查看详细信息&lt;/h3&gt;&lt;p style='color: #888888; font-size: 12px; line-height: 1.6;'&gt;• 单击设备卡片查看基本信息&lt;br/&gt;• 双击设备卡片打开工作台&lt;br/&gt;• 右键设备卡片可进行更多操作&lt;/p&gt;&lt;/div&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
     </property>
     <property name="wordWrap">
      <bool>true</bool>
     </property>
     <property name="textInteractionFlags">
      <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByMouse</set>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>