#ifndef MONITORCARD_H
#define MONITORCARD_H

#include <QFrame>
#include <QLabel>
#include <functional>

class QMenu;
class QAction;

class MonitorCard : public QFrame
{
    Q_OBJECT

public:

    enum DataType {
        Percentage,  // 百分比 (CPU)
        Memory,      // 内存 (MB/GB)
        Disk,        // 磁盘 (GB/TB)
        Network      // 网络 (KB/s, MB/s)
    };
    // 背景样式枚举
    enum BackgroundStyle {
        Default,     // 默认白色
        Glass,       // 玻璃效果
        Green,       // 绿色
        Orange,      // 橙色
        Blue,        // 蓝色
        CustomColor  // 自定义颜色
    };
    
    typedef std::function<QColor(int value)> ColorStrategy;
    
    explicit MonitorCard(
        const QString& label = "",
        const ColorStrategy& colorStrategy = defaultColorStrategy,
        DataType dataType = Percentage,
        QWidget *parent = nullptr
    );
    
    void setLabel(const QString &label);
    void setValue(int value, const QString& unit = QString());
    void setDataType(DataType type);
    int value() const { return m_value; }
    QString label() const { return m_label; }
    
    // 设置背景样式
    void setBackgroundStyle(BackgroundStyle style);
    BackgroundStyle backgroundStyle() const { return m_backgroundStyle; }
    
    // 设置自定义颜色
    void setCustomBackgroundColor(const QColor& color);
    
    // 设置自定义颜色策略
    void setColorStrategy(const ColorStrategy& strategy);
signals:
    void deleted(const QString& cardLabel);
protected:
    QPixmap drawCircularProgressBar(int value, int size);
    QColor getColorForValue(int value);
    
    void updateStyle();
    
    // 右键菜单相关
    void contextMenuEvent(QContextMenuEvent *event) override;
    void createContextMenu();
    
    QString m_label;
    int m_value;
    
    ColorStrategy m_colorStrategy;
    BackgroundStyle m_backgroundStyle;
    QColor m_customBackgroundColor;
    
    QLabel* m_iconLabel;
    QLabel* m_valueLabel;
    QLabel* m_nameLabel;
    
    bool m_hovered;
    
    // 右键菜单相关成员
    QMenu *m_contextMenu;
    QAction *m_deleteAction;
    QAction *m_backgroundDefaultAction;
    QAction *m_backgroundGlassAction;
    QAction *m_backgroundGreenAction;
    QAction *m_backgroundOrangeAction;
    QAction *m_backgroundBlueAction;
    QAction *m_backgroundCustomAction;

protected:
    void enterEvent(QEnterEvent *event) override;
    void leaveEvent(QEvent *event) override;
    
private slots:
    void onDeleteActionTriggered();
    void onBackgroundStyleChanged();
    
private:
    static QColor defaultColorStrategy(int value);
    DataType m_dataType;
    QString m_unit;
};

#endif // MONITORCARD_H
