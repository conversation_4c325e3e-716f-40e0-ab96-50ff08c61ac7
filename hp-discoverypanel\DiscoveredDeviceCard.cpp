#include "DiscoveredDeviceCard.h"
#include <QPainter>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QDateTime>
#include <QPainterPath>
#include <QStyle>

DiscoveredDeviceCard::DiscoveredDeviceCard(const DiscoveredDevice& device, bool isAdded, QWidget *parent)
    : Q<PERSON><PERSON>e(parent)
    , m_device(device)
    , m_hovered(false)
    , m_pressed(false)
{
    setStyleSheet(
        "DiscoveredDeviceCard { "
        "  background-color: #ffffff; "
        "  border: 1px solid #e0e0e0; "
        "  border-radius: 8px; "
        "  padding: 8px; "
        "}"
        "DiscoveredDeviceCard:hover { "
        "  border: 1px solid #4285f4; "
        "  background-color: #f8f9fa; "
        "}"
    );
    setCursor(Qt::PointingHandCursor);
    setFixedSize(200, 60);
    
    // 创建主布局
    QHBoxLayout* mainLayout = new QHBoxLayout(this);
    mainLayout->setContentsMargins(8, 6, 8, 6);
    mainLayout->setSpacing(8);
    
    // 创建信息布局
    QVBoxLayout* infoLayout = new QVBoxLayout();
    infoLayout->setContentsMargins(0, 0, 0, 0);
    infoLayout->setSpacing(2);
    
    // 设备名称
    m_nameLabel = new QLabel(m_device.name);
    m_nameLabel->setStyleSheet(
        "font-weight: bold; "
        "font-size: 12px; "
        "color: #202124;"
    );
    m_nameLabel->setWordWrap(false);
    
    // IP地址
    m_ipLabel = new QLabel(m_device.ip);
    m_ipLabel->setStyleSheet(
        "color: #5f6368; "
        "font-size: 11px;"
    );
    
    infoLayout->addWidget(m_nameLabel);
    infoLayout->addWidget(m_ipLabel);
    
    // 创建状态标签
    m_newLabel = new QLabel();
    if (isAdded) {
        m_newLabel->setStyleSheet(
            "color: #999999; "
            "font-size: 8px; "
            "font-weight: bold; "
            "padding: 2px 4px;"
        );
        m_newLabel->setText("已添加");
        setCursor(Qt::ArrowCursor);
        setProperty("isAdded", true);
    } else {
        m_newLabel->setStyleSheet(
            "color: #ff4444; "
            "font-size: 8px; "
            "font-weight: bold; "
            "padding: 2px 4px;"
        );
        m_newLabel->setText("New!!!");
        setProperty("isAdded", false);
    }
    m_newLabel->setAlignment(Qt::AlignCenter);
    
    // 添加到主布局
    mainLayout->addLayout(infoLayout);
    mainLayout->addStretch();
    mainLayout->addWidget(m_newLabel);
}

void DiscoveredDeviceCard::enterEvent(QEnterEvent *event)
{
    m_hovered = true;
    updateStyle();
    QFrame::enterEvent(event);
}

void DiscoveredDeviceCard::leaveEvent(QEvent *event)
{
    m_hovered = false;
    m_pressed = false;
    updateStyle();
    QFrame::leaveEvent(event);
}

// 在 DiscoveredDeviceCard.cpp 中修改鼠标事件处理函数
void DiscoveredDeviceCard::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        // 检查是否是已添加的设备
        bool isAdded = property("isAdded").toBool();
        // 只有未添加的设备才会显示按下效果
        if (!isAdded) {
            m_pressed = true;
            updateStyle();
        }
    }
    QFrame::mousePressEvent(event);
}

void DiscoveredDeviceCard::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        bool isAdded = property("isAdded").toBool();
        
        // 重置按下状态
        if (!isAdded) {
            m_pressed = false;
            updateStyle();
        }
        
        // 无论是否已添加，都发出点击信号
        if (rect().contains(event->pos())) {
            emit addDeviceClicked(m_device);  // 发送完整设备信息
        }
    }
    QFrame::mouseReleaseEvent(event);
}

void DiscoveredDeviceCard::paintEvent(QPaintEvent *event)
{
    QFrame::paintEvent(event);
    
    // 绘制按下效果
    if (m_pressed) {
        QPainter painter(this);
        painter.setRenderHint(QPainter::Antialiasing);
        
        // 绘制半透明覆盖层表示按下状态
        QPainterPath path;
        path.addRoundedRect(rect(), 8, 8);
        painter.fillPath(path, QColor(0, 0, 0, 20)); // 很轻微的黑色覆盖
    }
}

void DiscoveredDeviceCard::updateStyle()
{
    if (m_pressed) {
        setStyleSheet(
            "DiscoveredDeviceCard { "
            "  background-color: #e8eaed; "
            "  border: 1px solid #4285f4; "
            "  border-radius: 8px; "
            "  padding: 8px; "
            "}"
        );
    } else if (m_hovered) {
        setStyleSheet(
            "DiscoveredDeviceCard { "
            "  background-color: #f8f9fa; "
            "  border: 1px solid #4285f4; "
            "  border-radius: 8px; "
            "  padding: 8px; "
            "}"
        );
    } else {
        setStyleSheet(
            "DiscoveredDeviceCard { "
            "  background-color: #ffffff; "
            "  border: 1px solid #e0e0e0; "
            "  border-radius: 8px; "
            "  padding: 8px; "
            "}"
            "DiscoveredDeviceCard:hover { "
            "  border: 1px solid #4285f4; "
            "  background-color: #f8f9fa; "
            "}"
        );
    }
}

void DiscoveredDeviceCard::updateAddedStatus(bool isAdded)
{
    // 更新属性
    setProperty("isAdded", isAdded);
    
    // 更新标签显示
    if (isAdded) {
        m_newLabel->setStyleSheet(
            "color: #999999; "
            "font-size: 8px; "
            "font-weight: bold; "
            "padding: 2px 4px;"
        );
        m_newLabel->setText("已添加");
        setCursor(Qt::ArrowCursor);
    } else {
        m_newLabel->setStyleSheet(
            "color: #ff4444; "
            "font-size: 8px; "
            "font-weight: bold; "
            "padding: 2px 4px;"
        );
        m_newLabel->setText("New!!!");
        setCursor(Qt::PointingHandCursor);
    }
    
    // 更新样式
    style()->unpolish(this);
    style()->polish(this);
    update();
}
