// ws-monitorpanel/plotwidget.h
#ifndef PLOTWIDGET_H
#define PLOTWIDGET_H

#include <QWidget>
#include <QQueue>
#include <QJsonObject>
#include <QDateTime>

class PlotWidget : public QWidget
{
    Q_OBJECT

public:
    explicit PlotWidget(QWidget *parent = nullptr);
    ~PlotWidget();

    void addDataPoint(double value, qint64 timestamp);
    void setDataType(const QString& type);
    QString dataType() const { return m_dataType; }

public slots:
    void updatePlotData(const QJsonObject &data);
    void updateDataTypeSettings();
    void clearData();

protected:
    void paintEvent(QPaintEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private:
    // 计算适合的Y轴范围
    void calculateYRange(double& minY, double& maxY) const;
    // 格式化Y轴标签值
    QString formatYLabel(double value) const;
    
    QString m_dataType;
    QQueue<double> m_dataPoints;
    QQueue<qint64> m_timestamps;
    int m_maxDataPoints;
    qint64 m_timeWindow;
    qint64 m_lastUpdateTime;
    QColor m_plotColor;
    
    // 添加用于动态范围计算的成员变量
    mutable double m_currentMinY;
    mutable double m_currentMaxY;
    mutable bool m_rangeInitialized;
    QString m_unit;
};

#endif // PLOTWIDGET_H