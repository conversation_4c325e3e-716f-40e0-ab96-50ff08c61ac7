#ifndef MONITORUTILS_H
#define MONITORUTILS_H

#include <QColor>
#include <functional>

namespace MonitorUtils {
    // CPU颜色策略
    QColor cpuColorStrategy(int value);
    
    // 内存颜色策略
    QColor memoryColorStrategy(int value);
    
    // 硬盘颜色策略
    QColor diskColorStrategy(int value);
    
    // 网络颜色策略
    QColor networkColorStrategy(int value);
    
    // 温度颜色策略
    QColor temperatureColorStrategy(int value);
    
    // 默认颜色策略
    QColor defaultColorStrategy(int value);
}

#endif // MONITORUTILS_H