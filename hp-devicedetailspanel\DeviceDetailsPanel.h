// DeviceDetailsPanel.h
#ifndef DEVICEDETAILSPANEL_H
#define DEVICEDETAILSPANEL_H

#include <QFrame>
#include <QString>
#include "utils/deviceinfo.h"

namespace Ui {
class DeviceDetailsPanel;
}

struct DeviceInfo;

class DeviceDetailsPanel : public QFrame
{
    Q_OBJECT

public:
    explicit DeviceDetailsPanel(QWidget *parent = nullptr);
    ~DeviceDetailsPanel();

    void showDeviceDetails(const DeviceInfo* device);
    void clearDeviceDetails();
    QString getCurrentDeviceIP() const { return m_currentDeviceIP; }

private:
    void setupStyles();
    
    Ui::DeviceDetailsPanel *ui;
    QString m_currentDeviceIP;
};

#endif // DEVICEDETAILSPANEL_H