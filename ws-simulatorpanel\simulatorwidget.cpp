#include "simulatorwidget.h"
#include <QPushButton>
#include <QScrollBar>
#include <QDir>
#include <QJsonObject>
#include <QJsonArray>
#include "TCPSimulator.h"
#include "TCPServerSimulator.h"
#include "UDPSimulator.h"
#include <QMenu>
#include "simulatorcard.h"
#include "simulatorconfigdialog.h"

SimulatorWidget::SimulatorWidget(const QString& deviceId,const QString&ip,QWidget *parent)
    : QWidget(parent)
    , m_targetDeviceIP(ip)
    , m_deviceId(deviceId)
{
    setupUI();
    createSimulatorContextMenu();
}

SimulatorWidget::~SimulatorWidget()
{
    saveSimulatorConfig();
    qDeleteAll(m_simulators);
}

void SimulatorWidget::saveSimulatorConfig()
{
    if (m_deviceId.isEmpty()) {
        return;
    }
    
    // 构建配置文件路径
    QString appDir = QCoreApplication::applicationDirPath();
    QString deviceDir = appDir + "/devices/" + m_deviceId;
    QString configPath = deviceDir + "/simulator_config.json";
    
    // 创建设备目录（如果不存在）
    QDir dir(deviceDir);
    if (!dir.exists()) {
        dir.mkpath(".");
    }
    
    // 创建JSON对象来存储配置
    QJsonObject config;
    QJsonArray simulatorsArray;
    
    // 遍历所有模拟器并保存配置
    for (int i = 0; i < m_simulators.size(); ++i) {
        Simulator *simulator = m_simulators[i];
        QJsonObject simulatorObj;
        
        // 保存通用配置
        simulatorObj["id"] = i; // 使用索引作为ID
        simulatorObj["name"] = simulator->name();
        simulatorObj["targetAddress"] = simulator->targetAddress();
        simulatorObj["targetPort"] = simulator->targetPort();
        simulatorObj["messageInterval"] = simulator->messageInterval();
        simulatorObj["packetId"] = simulator->packetId();
        simulatorObj["isBigEndian"] = simulator->isBigEndian();
        
        // 根据模拟器类型保存特定配置
        if (qobject_cast<TCPSimulator*>(simulator)) {
            simulatorObj["type"] = "TCP";
        } else if (qobject_cast<TCPServerSimulator*>(simulator)) {
            simulatorObj["type"] = "TCPServer";
        } else if (qobject_cast<UDPSimulator*>(simulator)) {
            simulatorObj["type"] = "UDP";
        }

        // 保存报文字段配置
        QJsonArray fieldsArray;
        QList<PacketField> fields = simulator->packetFields();
        for (const PacketField &field : fields) {
            QJsonObject fieldObj;
            fieldObj["name"] = field.name();
            fieldObj["type"] = PacketField::typeToString(field.type());
            fieldObj["value"] = field.value().toString();
            fieldObj["stringLength"] = field.stringLength(); // 保存字符串长度
            fieldsArray.append(fieldObj);
        }
        simulatorObj["fields"] = fieldsArray;
        
        simulatorsArray.append(simulatorObj);
    }
    
    config["simulators"] = simulatorsArray;
    config["lastUpdated"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    // 保存到文件
    QFile configFile(configPath);
    if (configFile.open(QIODevice::WriteOnly)) {
        QJsonDocument doc(config);
        configFile.write(doc.toJson());
        configFile.close();
    }
}

void SimulatorWidget::loadSimulatorConfig()
{
    if (m_deviceId.isEmpty()) {
        return;
    }
    
    // 构建配置文件路径
    QString appDir = QCoreApplication::applicationDirPath();
    QString deviceDir = appDir + "/devices/" + m_deviceId;
    QString configPath = deviceDir + "/simulator_config.json";
    
    // 检查配置文件是否存在
    QFile configFile(configPath);
    if (!configFile.exists()) {
        return;
    }
    
    // 读取配置文件
    if (!configFile.open(QIODevice::ReadOnly)) {
        return;
    }
    
    QByteArray jsonData = configFile.readAll();
    configFile.close();
    
    QJsonDocument doc = QJsonDocument::fromJson(jsonData);
    if (!doc.isObject()) {
        return;
    }
    
    QJsonObject config = doc.object();
    if (!config.contains("simulators") || !config["simulators"].isArray()) {
        return;
    }
    
    QJsonArray simulatorsArray = config["simulators"].toArray();
    
    // 清除现有模拟器
    for (SimulatorCard *card : m_simulatorCards) {
        card->deleteLater();
    }
    m_simulatorCards.clear();
    
    for (Simulator *simulator : m_simulators) {
        simulator->deleteLater();
    }
    m_simulators.clear();
    
    // 创建模拟器
    for (int i = 0; i < simulatorsArray.size(); ++i) {
        QJsonObject simulatorObj = simulatorsArray[i].toObject();
        
        QString type = simulatorObj["type"].toString();
        Simulator *simulator = nullptr;
        
        // 根据类型创建相应的模拟器
        if (type == "TCP") {
            simulator = new TCPSimulator();
        } else if (type == "TCPServer") {
            simulator = new TCPServerSimulator();
        } else if (type == "UDP") {
            simulator = new UDPSimulator();
        }
        
        if (!simulator) {
            continue;
        }
        
        // 加载通用配置
        simulator->setName(simulatorObj["name"].toString());
        simulator->setTargetAddress(simulatorObj["targetAddress"].toString());
        simulator->setTargetPort(simulatorObj["targetPort"].toInt());
        simulator->setMessageInterval(simulatorObj["messageInterval"].toInt());
        simulator->setPacketId(simulatorObj["packetId"].toInt());
        simulator->setBigEndian(simulatorObj["isBigEndian"].toBool());
        
        // 加载报文字段配置
        if (simulatorObj.contains("fields") && simulatorObj["fields"].isArray()) {
            QJsonArray fieldsArray = simulatorObj["fields"].toArray();
            QList<PacketField> fields;

            for (int j = 0; j < fieldsArray.size(); ++j) {
                QJsonObject fieldObj = fieldsArray[j].toObject();
                PacketField field;
                field.setName(fieldObj["name"].toString());
                field.setType(PacketField::stringToType(fieldObj["type"].toString()));
                field.setValue(fieldObj["value"].toString());
                // 加载字符串长度，如果不存在则使用默认值10
                field.setStringLength(fieldObj.value("stringLength").toInt(10));
                fields.append(field);
            }

            simulator->setPacketFields(fields);
        }
        
        m_simulators.append(simulator);
        
        SimulatorCard *card = new SimulatorCard(simulator, this);
        connect(card, &SimulatorCard::removeRequested, this, &SimulatorWidget::removeSimulator);
        connect(card, &SimulatorCard::editRequested, this, &SimulatorWidget::editSimulator);
        connect(card, &SimulatorCard::startRequested, this, &SimulatorWidget::startSimulator);
        connect(card, &SimulatorCard::stopRequested, this, &SimulatorWidget::stopSimulator);
        connect(card, &SimulatorCard::logRequested, this, &SimulatorWidget::showSimulatorLog);
        
        m_simulatorCards.append(card);
        
        // 连接模拟器日志信号到统一日志显示
        connect(simulator, &Simulator::messageSent, this, &SimulatorWidget::appendLogMessage);
        connect(simulator, &Simulator::errorOccurred, this, &SimulatorWidget::appendLogMessage);
        connect(simulator, &QObject::destroyed, this, &SimulatorWidget::handleSimulatorDestroyed);
        
        // 添加这行：连接配置变化信号到卡片更新显示
        connect(simulator, &Simulator::configurationChanged, card, &SimulatorCard::updateDisplay);
        
        // 为该模拟器创建日志标签页
        createSimulatorLogTab(simulator);
    }
    
    // 更新布局
    updateLayout();
}

void SimulatorWidget::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(10);
    
    m_mainSplitter = new QSplitter(Qt::Vertical, this);
    
    // 上半部分：模拟器卡片区域
    QWidget *topWidget = new QWidget();
    QVBoxLayout *topLayout = new QVBoxLayout(topWidget);
    topLayout->setContentsMargins(0, 0, 0, 0);
    
    m_scrollArea = new QScrollArea();
    m_scrollArea->setWidgetResizable(true);
    m_scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    
    m_cardsContainer = new QWidget();
    m_cardsLayout = new QGridLayout(m_cardsContainer);
    m_cardsLayout->setAlignment(Qt::AlignTop);
    m_cardsLayout->setSpacing(10);
    
    m_scrollArea->setWidget(m_cardsContainer);
    topLayout->addWidget(m_scrollArea);
    
    m_mainSplitter->addWidget(topWidget);
    
    // 下半部分：日志显示区域（使用标签页）
    QWidget *logWidget = new QWidget();
    QVBoxLayout *logLayout = new QVBoxLayout(logWidget);
    logLayout->setContentsMargins(0, 0, 0, 0);
    
    // 日志控制栏
    QHBoxLayout *logControlLayout = new QHBoxLayout();
    logControlLayout->addWidget(new QLabel("日志过滤:"));
    
    m_logFilterEdit = new QLineEdit();
    m_logFilterEdit->setPlaceholderText("输入关键字过滤日志...");
    connect(m_logFilterEdit, &QLineEdit::textChanged, this, &SimulatorWidget::filterLogMessages);
    logControlLayout->addWidget(m_logFilterEdit);
    
    m_autoScrollCheckBox = new QCheckBox("自动滚动");
    m_autoScrollCheckBox->setChecked(true);
    logControlLayout->addWidget(m_autoScrollCheckBox);
    
    QPushButton *clearLogButton = new QPushButton("清空当前标签页日志");
    connect(clearLogButton, &QPushButton::clicked, this, &SimulatorWidget::clearLogMessages);
    logControlLayout->addWidget(clearLogButton);
    
    logLayout->addLayout(logControlLayout);
    
    // 日志标签页控件
    m_logTabWidget = new QTabWidget();
    m_logTabWidget->setTabsClosable(false);
    logLayout->addWidget(m_logTabWidget);
    
    m_mainSplitter->addWidget(logWidget);
    m_mainSplitter->setSizes(QList<int>() << 400 << 200); // 设置初始大小比例
    
    m_mainLayout->addWidget(m_mainSplitter);
}

void SimulatorWidget::createSimulatorContextMenu()
{
    m_contextMenu = new QMenu(this);
    
    QAction *addTCPSimulatorAction = new QAction("添加TCP模拟器", this);
    connect(addTCPSimulatorAction, &QAction::triggered, this, &SimulatorWidget::addTCPSimulator);
    
    QAction *addTCPServerSimulatorAction = new QAction("添加TCP服务器模拟器", this);
    connect(addTCPServerSimulatorAction, &QAction::triggered, this, &SimulatorWidget::addTCPServerSimulator);
    
    QAction *addUDPSimulatorAction = new QAction("添加UDP模拟器", this);
    connect(addUDPSimulatorAction, &QAction::triggered, this, &SimulatorWidget::addUDPSimulator);
    
    m_contextMenu->addAction(addTCPSimulatorAction);
    m_contextMenu->addAction(addTCPServerSimulatorAction);
    m_contextMenu->addAction(addUDPSimulatorAction);
}

void SimulatorWidget::contextMenuEvent(QContextMenuEvent *event)
{
    m_contextMenu->exec(event->globalPos());
}

void SimulatorWidget::addTCPSimulator()
{
    TCPSimulator *simulator = new TCPSimulator();
    simulator->setName(QString("TCP模拟器 %1").arg(m_simulators.size() + 1));
    simulator->setTargetAddress(m_targetDeviceIP.isEmpty() ? "127.0.0.1" : m_targetDeviceIP);
    simulator->setTargetPort(8080);
    simulator->setMessageInterval(1000);
    
    m_simulators.append(simulator);
    
    SimulatorCard *card = new SimulatorCard(simulator, this);
    connect(card, &SimulatorCard::removeRequested, this, &SimulatorWidget::removeSimulator);
    connect(card, &SimulatorCard::editRequested, this, &SimulatorWidget::editSimulator);
    connect(card, &SimulatorCard::startRequested, this, &SimulatorWidget::startSimulator);
    connect(card, &SimulatorCard::stopRequested, this, &SimulatorWidget::stopSimulator);
    connect(card, &SimulatorCard::logRequested, this, &SimulatorWidget::showSimulatorLog);
    
    m_simulatorCards.append(card);
    
    updateLayout();
    
    connect(simulator, &Simulator::messageSent, this, &SimulatorWidget::appendLogMessage);
    connect(simulator, &Simulator::errorOccurred, this, &SimulatorWidget::appendLogMessage);
    connect(simulator, &QObject::destroyed, this, &SimulatorWidget::handleSimulatorDestroyed);
    connect(simulator, &Simulator::configurationChanged, card, &SimulatorCard::updateDisplay);
 
    createSimulatorLogTab(simulator);
    
    appendLogMessage(QString("创建了新的TCP模拟器: %1 (未启动)").arg(simulator->name()));
    
    // 保存配置
    saveSimulatorConfig();
}

void SimulatorWidget::addUDPSimulator()
{
    UDPSimulator *simulator = new UDPSimulator();
    simulator->setName(QString("UDP模拟器 %1").arg(m_simulators.size() + 1));
    simulator->setTargetAddress(m_targetDeviceIP.isEmpty() ? "127.0.0.1" : m_targetDeviceIP);
    simulator->setTargetPort(8080);
    simulator->setMessageInterval(1000);
    
    m_simulators.append(simulator);
    
    SimulatorCard *card = new SimulatorCard(simulator, this);
    connect(card, &SimulatorCard::removeRequested, this, &SimulatorWidget::removeSimulator);
    connect(card, &SimulatorCard::editRequested, this, &SimulatorWidget::editSimulator);
    connect(card, &SimulatorCard::startRequested, this, &SimulatorWidget::startSimulator);
    connect(card, &SimulatorCard::stopRequested, this, &SimulatorWidget::stopSimulator);
    connect(card, &SimulatorCard::logRequested, this, &SimulatorWidget::showSimulatorLog);
    
    m_simulatorCards.append(card);
    
    updateLayout();
    
    connect(simulator, &Simulator::messageSent, this, &SimulatorWidget::appendLogMessage);
    connect(simulator, &Simulator::errorOccurred, this, &SimulatorWidget::appendLogMessage);
    connect(simulator, &QObject::destroyed, this, &SimulatorWidget::handleSimulatorDestroyed);
    connect(simulator, &Simulator::configurationChanged, card, &SimulatorCard::updateDisplay);
 
    createSimulatorLogTab(simulator);
    
    appendLogMessage(QString("创建了新的UDP模拟器: %1 (未启动)").arg(simulator->name()));
    
    // 保存配置
    saveSimulatorConfig();
}

void SimulatorWidget::addTCPServerSimulator()
{
    TCPServerSimulator *simulator = new TCPServerSimulator();
    simulator->setName(QString("TCP服务器模拟器 %1").arg(m_simulators.size() + 1));
    simulator->setTargetAddress("0.0.0.0");
    simulator->setTargetPort(8080);
    simulator->setMessageInterval(1000);
    
    m_simulators.append(simulator);
    
    SimulatorCard *card = new SimulatorCard(simulator, this);
    connect(card, &SimulatorCard::removeRequested, this, &SimulatorWidget::removeSimulator);
    connect(card, &SimulatorCard::editRequested, this, &SimulatorWidget::editSimulator);
    connect(card, &SimulatorCard::startRequested, this, &SimulatorWidget::startSimulator);
    connect(card, &SimulatorCard::stopRequested, this, &SimulatorWidget::stopSimulator);
    connect(card, &SimulatorCard::logRequested, this, &SimulatorWidget::showSimulatorLog);
    
    m_simulatorCards.append(card);
    
    updateLayout();
    
    connect(simulator, &Simulator::messageSent, this, &SimulatorWidget::appendLogMessage);
    connect(simulator, &Simulator::errorOccurred, this, &SimulatorWidget::appendLogMessage);
    connect(simulator, &QObject::destroyed, this, &SimulatorWidget::handleSimulatorDestroyed);
    connect(simulator, &Simulator::configurationChanged, card, &SimulatorCard::updateDisplay);
 
    createSimulatorLogTab(simulator);
    
    appendLogMessage(QString("创建了新的TCP服务器模拟器: %1 (未启动)").arg(simulator->name()));
    
    // 保存配置
    saveSimulatorConfig();
}

void SimulatorWidget::createSimulatorLogTab(Simulator *simulator)
{
    // 创建一个新的文本编辑器用于显示该模拟器的日志
    QPlainTextEdit *logEditor = new QPlainTextEdit();
    logEditor->setReadOnly(true);
    
    // 将模拟器和其日志编辑器关联起来
    m_simulatorLogEditors[simulator] = logEditor;
    
    // 添加标签页，使用模拟器名称作为标签标题
    m_logTabWidget->addTab(logEditor, simulator->name());
}

void SimulatorWidget::removeSimulatorLogTab(Simulator *simulator)
{
    // 查找并移除该模拟器的日志标签页
    QPlainTextEdit *logEditor = m_simulatorLogEditors.value(simulator);
    if (logEditor) {
        int index = m_logTabWidget->indexOf(logEditor);
        if (index != -1) {
            m_logTabWidget->removeTab(index);
        }
        m_simulatorLogEditors.remove(simulator);
        // 注意：不需要手动删除logEditor，因为Qt会自动处理
    }
}

void SimulatorWidget::removeSimulator(SimulatorCard *card)
{
    int index = m_simulatorCards.indexOf(card);
    if (index != -1) {
        Simulator *simulator = m_simulators[index];
        
        appendLogMessage(QString("删除了模拟器: %1").arg(simulator->name()));
        
        removeSimulatorLogTab(simulator);
        
        m_simulatorCards.removeAt(index);
        m_simulators.takeAt(index);
        m_simulatorLogs.remove(simulator);
        card->deleteLater();
        
        updateLayout();
        
        // 重新编号剩余的模拟器
        for (int i = 0; i < m_simulators.size(); ++i) {
            Simulator *s = m_simulators[i];
            QString typeStr = "";
            
            if (qobject_cast<TCPSimulator*>(s)) {
                typeStr = "TCP模拟器";
            } else if (qobject_cast<TCPServerSimulator*>(s)) {
                typeStr = "TCP服务器模拟器";
            } else if (qobject_cast<UDPSimulator*>(s)) {
                typeStr = "UDP模拟器";
            }
            
            if (!typeStr.isEmpty()) {
                s->setName(QString("%1 %2").arg(typeStr).arg(i + 1));
            }
        }
        
        // 保存配置
        saveSimulatorConfig();
    }
}

void SimulatorWidget::editSimulator(SimulatorCard *card)
{
    int index = m_simulatorCards.indexOf(card);
    if (index != -1) {
        Simulator *simulator = m_simulators[index];
        SimulatorConfigDialog dialog(simulator, this);
        int result = dialog.exec();
        
        if (result == QDialog::Accepted) {
            card->update();
            card->updateDisplay();
            
            QPlainTextEdit *logEditor = m_simulatorLogEditors.value(simulator);
            if (logEditor) {
                int tabIndex = m_logTabWidget->indexOf(logEditor);
                if (tabIndex != -1) {
                    m_logTabWidget->setTabText(tabIndex, simulator->name());
                }
            }
            
            // 保存配置
            saveSimulatorConfig();
        }
    }
}

void SimulatorWidget::handleSimulatorDestroyed(QObject *obj)
{
    // 当模拟器对象被销毁时，清理相关资源
    Simulator *simulator = static_cast<Simulator*>(obj);
    m_simulatorLogEditors.remove(simulator);
    m_simulatorLogs.remove(simulator);
}

void SimulatorWidget::appendLogMessage(const QString &message)
{
    // 获取发送信号的模拟器
    Simulator *senderSimulator = qobject_cast<Simulator*>(sender());
    QString formattedMessage;
    
    if (senderSimulator) {
        // 为日志添加时间戳和模拟器名称
        formattedMessage = QString("[%1] %2: %3")
                          .arg(QTime::currentTime().toString("hh:mm:ss.zzz"))
                          .arg(senderSimulator->name())
                          .arg(message);
        
        // 将日志添加到该模拟器的专用日志编辑器中
        QPlainTextEdit *logEditor = m_simulatorLogEditors.value(senderSimulator);
        if (logEditor) {
            logEditor->appendPlainText(formattedMessage);
            
            // 自动滚动到最新日志
            if (m_autoScrollCheckBox->isChecked()) {
                logEditor->verticalScrollBar()->setValue(logEditor->verticalScrollBar()->maximum());
            }
        }
    } else {
        // 如果不是从模拟器发送的信号（如创建/删除模拟器的日志）
        formattedMessage = QString("[%1] 系统: %2")
                          .arg(QTime::currentTime().toString("hh:mm:ss.zzz"))
                          .arg(message);
    }
    
    // 应用过滤器（如果需要的话）
    if (!m_logFilterEdit->text().isEmpty()) {
        if (!formattedMessage.contains(m_logFilterEdit->text(), Qt::CaseInsensitive)) {
            return;
        }
    }
    
    // 这里可以添加全局日志显示或者其他处理
}

void SimulatorWidget::updateLayout()
{
    QLayoutItem *child;
    while ((child = m_cardsLayout->takeAt(0)) != 0) {
        delete child;
    }
    
    for (int i = 0; i < m_simulatorCards.size(); ++i) {
        m_cardsLayout->addWidget(m_simulatorCards[i]);
    }
}

void SimulatorWidget::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    // 窗口大小改变时更新布局
    updateLayout();
}

void SimulatorWidget::startSimulator(SimulatorCard *card)
{
    int index = m_simulatorCards.indexOf(card);
    if (index != -1) {
        Simulator *simulator = m_simulators[index];
        simulator->start();
        appendLogMessage(QString("启动模拟器: %1").arg(simulator->name()));
    }
}

void SimulatorWidget::stopSimulator(SimulatorCard *card)
{
    int index = m_simulatorCards.indexOf(card);
    if (index != -1) {
        Simulator *simulator = m_simulators[index];
        simulator->stop();
        appendLogMessage(QString("停止模拟器: %1").arg(simulator->name()));
    }
}

void SimulatorWidget::showSimulatorLog(SimulatorCard *card)
{
    // 切换到指定模拟器的日志标签页
    Simulator *simulator = card->getSimulator();
    QPlainTextEdit *logEditor = m_simulatorLogEditors.value(simulator);
    if (logEditor) {
        int tabIndex = m_logTabWidget->indexOf(logEditor);
        if (tabIndex != -1) {
            m_logTabWidget->setCurrentIndex(tabIndex);
        }
    }
}

void SimulatorWidget::clearLogMessages()
{
    // 清空当前选中的标签页日志
    int currentIndex = m_logTabWidget->currentIndex();
    if (currentIndex >= 0) {
        QWidget *currentWidget = m_logTabWidget->widget(currentIndex);
        QPlainTextEdit *logEditor = qobject_cast<QPlainTextEdit*>(currentWidget);
        if (logEditor) {
            logEditor->clear();
        }
    }
}

void SimulatorWidget::filterLogMessages()
{
    // 过滤日志消息的实现
    // 在当前实现中，过滤是在添加日志时进行的
    // 如果需要实时过滤显示的日志，可以在这里实现
}
