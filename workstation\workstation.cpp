#include "workstation.h"
#include "ui_workstation.h"
#include <QTimer>
#include <QProcess>
#include <QFile>
#include <QDir>
#include <QFileDialog>
#include <QMessageBox>
#include <QStandardPaths>
#include <QRandomGenerator>
#include <QListWidgetItem>
#include <QTreeWidgetItem>
#include <QMenu>
#include <QAction>
#include <QDateTime>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include "ws-monitorpanel/monitorselector.h"
#include "ws-simulatorpanel/simulatorwidget.h"
#include "device_connection_manager.h"
#include "homepage/mainwindow.h"
#ifdef Q_OS_WIN
#include <windows.h>
#endif

WorkStation::WorkStation(const QString& deviceName, const QString& deviceId, const QString& deviceIP, int devicePort, MainWindow* mainWindow, QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::WorkStation)
    , m_deviceName(deviceName)
    , m_deviceId(deviceId)
    , m_deviceIP(deviceIP)
    , m_devicePort(devicePort)
    , m_mainWindow(mainWindow)
    , m_simulatorWidget(nullptr)
    , m_monitorPanel(nullptr)
{
    ui->setupUi(this);
    setWindowTitle(QString("工作台 - %1 (%2)").arg(deviceName, deviceIP));
    
    setWindowFlags(Qt::Window);

    m_connectionManager = new DeviceConnectionManager(this);
    m_connectionManager->connectToDevice(deviceIP, devicePort);

    setupUI();
    
    if (m_mainWindow) {
        m_mainWindow->hide();
    }
}

WorkStation::~WorkStation()
{
    delete ui;
}

void WorkStation::setupUI()
{
    // 创建tab widget
    QTabWidget *tabWidget = new QTabWidget(this);
    
    // 创建并设置监控面板
    m_monitorPanel = new MonitorPanel(m_deviceId, m_deviceIP, m_devicePort, m_connectionManager, this);
    m_monitorPanel->loadMonitorConfig();
    tabWidget->addTab(m_monitorPanel, "系统监控");
    
    // 创建并添加模拟器面板
    m_simulatorWidget = new SimulatorWidget(m_deviceId,m_deviceIP,this);
    m_simulatorWidget->loadSimulatorConfig();
    tabWidget->addTab(m_simulatorWidget, "模拟器管理");
    
    // 创建并设置配置管理面板（传递设备ID、IP和端口）
    m_configPanel = new ConfigPanel(m_deviceId, m_deviceIP, m_devicePort, m_connectionManager, this);
    tabWidget->addTab(m_configPanel, "配置管理");
    
    // 添加tab widget到主布局
    ui->verticalLayout->addWidget(tabWidget);
}

void WorkStation::closeEvent(QCloseEvent *event)
{
    // 保存模拟器配置
    if (m_simulatorWidget) {
        m_simulatorWidget->saveSimulatorConfig();
    }

    if(m_monitorPanel)
    {
        m_monitorPanel->saveMonitorConfig();
    }

    if(m_connectionManager)
    {
        m_connectionManager->disconnectFromDevice();
    }

    // 发射工作台关闭信号
    emit workstationClosed();
    
    // 显示主窗口
    if (m_mainWindow) {
        m_mainWindow->show();
    }
    
    QMainWindow::closeEvent(event);
}
