// 修改 DiscoveredDevicesManager.cpp
#include "DiscoveredDevicesManager.h"
#include <QNetworkInterface>
#include <QTimer>
#include <QDateTime>
#include <QDebug>
#include <QRegularExpression>

DiscoveredDevicesManager::DiscoveredDevicesManager(QObject *parent)
    : QObject(parent)
    , m_statusCheckTimer(new QTimer(this))
{
    connect(m_statusCheckTimer, &QTimer::timeout, this, &DiscoveredDevicesManager::checkDeviceTimeout);
    m_statusCheckTimer->start(1000); // 每秒检查一次设备超时
}

DiscoveredDevicesManager::~DiscoveredDevicesManager()
{
    stopDiscovery();
}

void DiscoveredDevicesManager::startDiscovery()
{
    // 获取所有活动的网络接口
    QList<QNetworkInterface> interfaces = QNetworkInterface::allInterfaces();
    
    for (const QNetworkInterface &interface : interfaces) {
        // 只处理正在运行且支持多播的接口
        if (interface.flags().testFlag(QNetworkInterface::IsUp) &&
            interface.flags().testFlag(QNetworkInterface::IsRunning) &&
            interface.flags().testFlag(QNetworkInterface::CanMulticast)) {
            
            QString interfaceName = interface.name();
            
            // 为每个接口创建一个 DiscoveryListener
            if (!m_discoveryListeners.contains(interfaceName)) {
                DiscoveryListener *listener = new DiscoveryListener(this);
                connect(listener, &DiscoveryListener::deviceResponseReceived,
                        this, &DiscoveredDevicesManager::onDeviceResponseReceived);
                
                listener->start(interface);
                m_discoveryListeners[interfaceName] = listener;
                qDebug() << "Started discovery on interface:" << interfaceName;
            }
        }
    }
}

void DiscoveredDevicesManager::stopDiscovery()
{
    for (DiscoveryListener *listener : m_discoveryListeners.values()) {
        listener->stop();
        listener->deleteLater();
    }
    m_discoveryListeners.clear();
}

void DiscoveredDevicesManager::onDeviceResponseReceived(const DiscoveredDevice &device)
{
    // 如果设备已存在，更新时间戳和其他信息
    if (m_discoveredDevices.contains(device.ip)) {
        m_discoveredDevices[device.ip] = device;
    } else {
        // 新设备
        m_discoveredDevices[device.ip] = device;
        
        // 发送设备发现信号（传递完整设备信息）
        emit deviceDiscovered(device);
    }
}

void DiscoveredDevicesManager::checkDeviceTimeout()
{
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    QList<QString> devicesToRemove;

    // 查找超时的设备
    for (auto it = m_discoveredDevices.begin(); it != m_discoveredDevices.end(); ) {
        if (currentTime - it.value().lastSeen > DEVICE_TIMEOUT_MS) {
            devicesToRemove.append(it.key());
            it = m_discoveredDevices.erase(it);
        } else {
            ++it;
        }
    }
    
    // 发送设备移除信号
    for (const QString &deviceIP : devicesToRemove) {
        emit deviceRemoved(deviceIP);
    }
}

bool DiscoveredDevicesManager::isDeviceOnline(const QString &deviceIP) const
{
    if (m_discoveredDevices.contains(deviceIP)) {
        qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
        return (currentTime - m_discoveredDevices[deviceIP].lastSeen) <= DEVICE_TIMEOUT_MS;
    }
    return false;
}

QStringList DiscoveredDevicesManager::getDiscoveredDevices() const
{
    return m_discoveredDevices.keys();
}