#include "theme_manager.h"

ThemeManager* ThemeManager::s_instance = nullptr;

ThemeManager::ThemeManager(QObject *parent)
    : QObject(parent)
    , m_currentTheme(Light)
{
    s_instance = this;
}

ThemeManager* ThemeManager::instance()
{
    return s_instance;
}

void ThemeManager::initialize()
{
    QSettings settings;
    int savedTheme = settings.value("theme", Light).toInt();
    setTheme(static_cast<Theme>(savedTheme));
}

void ThemeManager::setTheme(Theme theme)
{
    if (m_currentTheme == theme)
        return;

    m_currentTheme = theme;
    
    QSettings settings;
    settings.setValue("theme", theme);
    
    QString stylesheet = getStyleSheet();
    qApp->setStyleSheet(stylesheet);
    
    emit themeChanged(theme);
}

QString ThemeManager::getStyleSheet() const
{
    if (m_currentTheme == Dark) {
        return R"(
            QMainWindow, QWidget {
                background-color: #2d2d2d;
                color: #ffffff;
            }
            
            QMenuBar {
                background-color: #3d3d3d;
                color: #ffffff;
            }
            
            QMenuBar::item {
                background: transparent;
                padding: 4px 8px;
            }
            
            QMenuBar::item:selected {
                background: #5d5d5d;
            }
            
            QMenuBar::item:pressed {
                background: #4d4d4d;
            }
            
            QMenu {
                background-color: #3d3d3d;
                color: #ffffff;
                border: 1px solid #5d5d5d;
            }
            
            QMenu::item {
                padding: 4px 20px;
            }
            
            QMenu::item:selected {
                background-color: #4d4d4d;
            }
            
            QToolBar {
                background: #3d3d3d;
                border: none;
            }
            
            QPushButton {
                background-color: #3d3d3d;
                border: 1px solid #5d5d5d;
                color: #ffffff;
                padding: 6px 12px;
                border-radius: 4px;
            }
            
            QPushButton:hover {
                background-color: #4d4d4d;
            }
            
            QPushButton:pressed {
                background-color: #5d5d5d;
            }
            
            QPushButton:disabled {
                background-color: #2a2a2a;
                color: #777777;
            }
            
            QGroupBox {
                background-color: #2d2d2d;
                border: 1px solid #5d5d5d;
                margin-top: 1ex;
                border-radius: 4px;
                color: #ffffff;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                background-color: #2d2d2d;
            }
            
            QLabel {
                color: #ffffff;
            }
            
            QLineEdit, QTextEdit, QPlainTextEdit {
                background-color: #1d1d1d;
                border: 1px solid #5d5d5d;
                color: #ffffff;
                border-radius: 4px;
                padding: 4px;
            }
            
            QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
                border: 1px solid #4285f4;
            }
            
            QScrollBar:vertical {
                border: none;
                background: #1d1d1d;
                width: 15px;
                margin: 0px 0px 0px 0px;
            }
            
            QScrollBar::handle:vertical {
                background: #5d5d5d;
                border-radius: 4px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #6d6d6d;
            }
            
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            
            QScrollBar:horizontal {
                border: none;
                background: #1d1d1d;
                height: 15px;
                margin: 0px 0px 0px 0px;
            }
            
            QScrollBar::handle:horizontal {
                background: #5d5d5d;
                border-radius: 4px;
                min-width: 20px;
            }
            
            QScrollBar::handle:horizontal:hover {
                background: #6d6d6d;
            }
            
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }
            
            QTabWidget::pane {
                border: 1px solid #5d5d5d;
            }
            
            QTabBar::tab {
                background: #3d3d3d;
                border: 1px solid #5d5d5d;
                border-bottom-color: #5d5d5d;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                min-width: 8ex;
                padding: 4px;
                color: #ffffff;
            }
            
            QTabBar::tab:selected, QTabBar::tab:hover {
                background: #4d4d4d;
            }
            
            QTabBar::tab:selected {
                background: #4d4d4d;
                border-bottom-color: #4d4d4d;
            }
            
            QHeaderView::section {
                background-color: #3d3d3d;
                color: #ffffff;
                padding: 4px;
                border: 1px solid #5d5d5d;
            }
            
            QTableWidget, QTreeWidget {
                background-color: #1d1d1d;
                alternate-background-color: #2a2a2a;
                color: #ffffff;
                gridline-color: #5d5d5d;
            }
            
            QTableWidget::item:selected, QTreeWidget::item:selected {
                background-color: #4285f4;
            }
            
            QListWidget {
                background-color: #1d1d1d;
                color: #ffffff;
                border: 1px solid #5d5d5d;
            }
            
            QListWidget::item:selected {
                background-color: #4285f4;
            }
            
            QComboBox {
                background-color: #3d3d3d;
                border: 1px solid #5d5d5d;
                color: #ffffff;
                border-radius: 4px;
                padding: 4px;
            }
            
            QComboBox QAbstractItemView {
                background-color: #3d3d3d;
                border: 1px solid #5d5d5d;
                color: #ffffff;
            }
            
            QCheckBox {
                color: #ffffff;
            }
            
            QRadioButton {
                color: #ffffff;
            }
            
            QStatusBar {
                background-color: #3d3d3d;
                color: #ffffff;
            }
            
            QFrame {
                background-color: #2d2d2d;
                border: 1px solid #5d5d5d;
            }
            
            DeviceCard {
                background-color: #3d3d3d;
                border: 1px solid #5d5d5d;
                border-radius: 8px;
            }
            
            DeviceCard:hover {
                border: 1px solid #4285f4;
                background-color: #4d4d4d;
            }
            
            AddMonitorCard {
                background-color: #3d3d3d;
                border: 1px solid #5d5d5d;
                border-radius: 8px;
            }
            
            AddMonitorCard:hover {
                border: 1px solid #4285f4;
                background-color: #4d4d4d;
            }
            
            MonitorCard {
                background-color: #3d3d3d;
                border: 1px solid #5d5d5d;
                border-radius: 8px;
            }
            
            MonitorCard:hover {
                border: 1px solid #4285f4;
                background-color: #4d4d4d;
            }
            
            DiscoveredDeviceCard {
                background-color: #3d3d3d;
                border: 1px solid #5d5d5d;
                border-radius: 8px;
            }
            
            DiscoveredDeviceCard:hover {
                border: 1px solid #4285f4;
                background-color: #4d4d4d;
            }
            
            SimulatorCard {
                background-color: #3d3d3d;
                border: 1px solid #5d5d5d;
                border-radius: 8px;
            }
            
            SimulatorCard:hover {
                background-color: #4d4d4d;
                border: 1px solid #5d5d5d;
            }
        )";
    } else {
        // 默认明亮主题
        return R"(
            DeviceCard {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
            }
            
            DeviceCard:hover {
                border: 1px solid #4285f4;
                background-color: #f8f9fa;
            }
            
            AddMonitorCard {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
            }
            
            AddMonitorCard:hover {
                border: 1px solid #4285f4;
                background-color: #f8f9fa;
            }
            
            MonitorCard {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
            }
            
            MonitorCard:hover {
                border: 1px solid #4285f4;
                background-color: #f8f9fa;
            }
            
            DiscoveredDeviceCard {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
            }
            
            DiscoveredDeviceCard:hover {
                border: 1px solid #4285f4;
                background-color: #f8f9fa;
            }
            
            SimulatorCard {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
            
            SimulatorCard:hover {
                background-color: #e9ecef;
                border: 1px solid #adb5bd;
            }
        )";
    }
}