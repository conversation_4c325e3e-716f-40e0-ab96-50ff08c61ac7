#include "simulatorconfigdialog.h"
#include "Simulator.h"
#include <QVBoxLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QHeaderView>
#include <QPushButton>
SimulatorConfigDialog::SimulatorConfigDialog(Simulator *simulator, QWidget *parent)
    : QDialog(parent)
    , m_simulator(simulator)
{
    setupUI();
    loadSettings();
    setWindowTitle(QString("编辑 %1").arg(m_simulator->name()));
    resize(600, 500);
}

void SimulatorConfigDialog::setupUI()
{
    QVBoxLayout *layout = new QVBoxLayout(this);
    
    QTabWidget *tabWidget = new QTabWidget;
    
    // 基本设置标签页
    QWidget *basicWidget = new QWidget;
    QFormLayout *basicLayout = new QFormLayout(basicWidget);
    
    m_nameEdit = new QLineEdit(this);
    basicLayout->addRow("名称:", m_nameEdit);
    
    m_targetAddressEdit = new QLineEdit(this);
    basicLayout->addRow("目标地址:", m_targetAddressEdit);
    
    m_targetPortSpinBox = new QSpinBox(this);
    m_targetPortSpinBox->setRange(1, 65535);
    m_targetPortSpinBox->setValue(8080);
    basicLayout->addRow("目标端口:", m_targetPortSpinBox);
    
    m_messageIntervalSpinBox = new QSpinBox(this);
    m_messageIntervalSpinBox->setRange(10, 10000);
    m_messageIntervalSpinBox->setValue(1000);
    m_messageIntervalSpinBox->setSuffix(" ms");
    basicLayout->addRow("消息间隔:", m_messageIntervalSpinBox);
    
    tabWidget->addTab(basicWidget, "基本设置");
    
    // 报文配置标签页
    QWidget *packetWidget = new QWidget;
    QVBoxLayout *packetLayout = new QVBoxLayout(packetWidget);
    
    // 报文头配置
    QGroupBox *headerGroup = new QGroupBox("报文头配置");
    QFormLayout *headerLayout = new QFormLayout(headerGroup);
    
    m_packetIdSpinBox = new QSpinBox;
    m_packetIdSpinBox->setRange(0, 65535);
    m_packetIdSpinBox->setValue(200);
    headerLayout->addRow("报文ID:", m_packetIdSpinBox);
    
    m_bigEndianCheckBox = new QCheckBox("大端序");
    m_bigEndianCheckBox->setChecked(false);
    headerLayout->addRow("字节序:", m_bigEndianCheckBox);
    
    packetLayout->addWidget(headerGroup);
    
    // 数据区配置
    QGroupBox *dataGroup = new QGroupBox("数据区配置");
    QVBoxLayout *dataLayout = new QVBoxLayout(dataGroup);
    
    // 字段表格
    m_fieldsTable = new QTableWidget(0, 4);
    m_fieldsTable->setHorizontalHeaderLabels({"字段名", "类型", "值", "长度"});
    m_fieldsTable->horizontalHeader()->setStretchLastSection(true);
    m_fieldsTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    dataLayout->addWidget(m_fieldsTable);
    
    // 按钮布局
    QHBoxLayout *buttonLayout = new QHBoxLayout;
    m_addFieldButton = new QPushButton("添加字段");
    m_removeFieldButton = new QPushButton("删除字段");
    m_batchAddButton = new QPushButton("批量添加");
    buttonLayout->addWidget(m_addFieldButton);
    buttonLayout->addWidget(m_removeFieldButton);
    buttonLayout->addWidget(m_batchAddButton);
    buttonLayout->addStretch();
    dataLayout->addLayout(buttonLayout);
    
    // 报文信息
    m_packetInfoLabel = new QLabel;
    dataLayout->addWidget(m_packetInfoLabel);
    
    packetLayout->addWidget(dataGroup);
    
    tabWidget->addTab(packetWidget, "报文配置");
    
    layout->addWidget(tabWidget);
    
    m_buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel, this);
    connect(m_buttonBox, &QDialogButtonBox::accepted, this, &SimulatorConfigDialog::saveSettings);
    connect(m_buttonBox, &QDialogButtonBox::rejected, this, &QDialog::reject);
    
    layout->addWidget(m_buttonBox);
    
    // 连接信号
    connect(m_addFieldButton, &QPushButton::clicked, this, &SimulatorConfigDialog::addField);
    connect(m_removeFieldButton, &QPushButton::clicked, this, &SimulatorConfigDialog::removeField);
    connect(m_batchAddButton, &QPushButton::clicked, this, &SimulatorConfigDialog::batchAddFields);
    connect(m_fieldsTable, &QTableWidget::cellChanged, this, &SimulatorConfigDialog::updatePacketInfo);
    connect(m_packetIdSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &SimulatorConfigDialog::updatePacketInfo);
}

void SimulatorConfigDialog::batchAddFields()
{
    // 创建批量添加对话框
    QDialog batchDialog(this);
    batchDialog.setWindowTitle("批量添加字段");
    batchDialog.setModal(true);
    
    QVBoxLayout *layout = new QVBoxLayout(&batchDialog);
    
    // 字段数量
    QSpinBox *countSpinBox = new QSpinBox();
    countSpinBox->setRange(1, 10000);
    countSpinBox->setValue(10);
    
    // 字段类型
    QComboBox *typeCombo = new QComboBox();
    typeCombo->addItem("BOOL");
    typeCombo->addItem("BYTE");
    typeCombo->addItem("INT");
    typeCombo->addItem("DINT");
    typeCombo->addItem("WORD");
    typeCombo->addItem("DWORD");
    typeCombo->addItem("REAL");
    typeCombo->addItem("STRING");
    typeCombo->setCurrentText("INT");
    
    // 字段名前缀
    QLineEdit *prefixEdit = new QLineEdit("field");
    
    QFormLayout *formLayout = new QFormLayout();
    formLayout->addRow("字段数量:", countSpinBox);
    formLayout->addRow("字段类型:", typeCombo);
    formLayout->addRow("字段名前缀:", prefixEdit);
    
    layout->addLayout(formLayout);
    
    QDialogButtonBox *buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
    layout->addWidget(buttonBox);
    
    connect(buttonBox, &QDialogButtonBox::accepted, &batchDialog, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, &batchDialog, &QDialog::reject);
    
    if (batchDialog.exec() == QDialog::Accepted) {
        int count = countSpinBox->value();
        QString type = typeCombo->currentText();
        QString prefix = prefixEdit->text();
        
        for (int i = 0; i < count; ++i) {
            int row = m_fieldsTable->rowCount();
            m_fieldsTable->insertRow(row);
            
            // 字段名
            QTableWidgetItem *nameItem = new QTableWidgetItem(QString("%1_%2").arg(prefix).arg(row + 1));
            m_fieldsTable->setItem(row, 0, nameItem);
            
            // 类型下拉框
            QComboBox *fieldTypeCombo = new QComboBox();
            fieldTypeCombo->addItem("BOOL");
            fieldTypeCombo->addItem("BYTE");
            fieldTypeCombo->addItem("INT");
            fieldTypeCombo->addItem("DINT");
            fieldTypeCombo->addItem("WORD");
            fieldTypeCombo->addItem("DWORD");
            fieldTypeCombo->addItem("REAL");
            fieldTypeCombo->addItem("STRING");
            fieldTypeCombo->setCurrentText(type);
            m_fieldsTable->setCellWidget(row, 1, fieldTypeCombo);
            m_typeComboBoxes[row] = fieldTypeCombo;
            
            // 默认值
            QString defaultValue = (type == "STRING") ? "" : "0";
            QTableWidgetItem *valueItem = new QTableWidgetItem(defaultValue);
            m_fieldsTable->setItem(row, 2, valueItem);

            // 长度列（仅对STRING类型有效）
            QSpinBox *lengthSpinBox = new QSpinBox();
            lengthSpinBox->setRange(1, 1000);
            lengthSpinBox->setValue(10);
            lengthSpinBox->setEnabled(type == "STRING");
            m_fieldsTable->setCellWidget(row, 3, lengthSpinBox);

            // 连接信号
            connect(fieldTypeCombo, &QComboBox::currentTextChanged, this, &SimulatorConfigDialog::updatePacketInfo);
            connect(fieldTypeCombo, &QComboBox::currentTextChanged, this, &SimulatorConfigDialog::onFieldTypeChanged);
            connect(lengthSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &SimulatorConfigDialog::updatePacketInfo);
        }
        
        updatePacketInfo();
    }
}

void SimulatorConfigDialog::loadSettings()
{
    if (m_simulator) {
        m_nameEdit->setText(m_simulator->name());
        m_targetAddressEdit->setText(m_simulator->targetAddress());
        m_targetPortSpinBox->setValue(m_simulator->targetPort());
        m_messageIntervalSpinBox->setValue(m_simulator->messageInterval());
        
        // 加载报文配置
        m_packetIdSpinBox->setValue(m_simulator->packetId());
        m_bigEndianCheckBox->setChecked(m_simulator->isBigEndian()); // 加载实际设置的字节序
        
        // 加载字段
        QList<PacketField> fields = m_simulator->packetFields();
        m_fieldsTable->setRowCount(fields.size());
        m_typeComboBoxes.clear();
        
        for (int i = 0; i < fields.size(); ++i) {
            const PacketField& field = fields[i];
            
            QTableWidgetItem *nameItem = new QTableWidgetItem(field.name());
            m_fieldsTable->setItem(i, 0, nameItem);
            
            // 创建类型下拉框
            QComboBox *typeCombo = new QComboBox();
            typeCombo->addItem("BOOL");
            typeCombo->addItem("BYTE");
            typeCombo->addItem("INT");
            typeCombo->addItem("DINT");
            typeCombo->addItem("WORD");
            typeCombo->addItem("DWORD");
            typeCombo->addItem("REAL");
            typeCombo->addItem("STRING");
            typeCombo->setCurrentText(PacketField::typeToString(field.type()));
            m_fieldsTable->setCellWidget(i, 1, typeCombo);
            m_typeComboBoxes[i] = typeCombo;
            
            QTableWidgetItem *valueItem = new QTableWidgetItem(field.value().toString());
            m_fieldsTable->setItem(i, 2, valueItem);

            // 长度列（仅对STRING类型有效）
            QSpinBox *lengthSpinBox = new QSpinBox();
            lengthSpinBox->setRange(1, 1000);
            lengthSpinBox->setValue(field.stringLength());
            lengthSpinBox->setEnabled(field.type() == PacketField::STRING);
            m_fieldsTable->setCellWidget(i, 3, lengthSpinBox);

            // 连接信号
            connect(typeCombo, &QComboBox::currentTextChanged, this, &SimulatorConfigDialog::updatePacketInfo);
            connect(typeCombo, &QComboBox::currentTextChanged, this, &SimulatorConfigDialog::onFieldTypeChanged);
            connect(lengthSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &SimulatorConfigDialog::updatePacketInfo);
        }
        
        updatePacketInfo();
        
        // 如果模拟器正在运行，则禁用所有编辑控件
        bool isRunning = m_simulator->isRunning();
        if (isRunning) {
            m_nameEdit->setReadOnly(true);
            m_targetAddressEdit->setReadOnly(true);
            m_targetPortSpinBox->setEnabled(false);
            m_messageIntervalSpinBox->setEnabled(false);
            m_packetIdSpinBox->setEnabled(false);
            m_bigEndianCheckBox->setEnabled(false);
            m_addFieldButton->setEnabled(false);
            m_removeFieldButton->setEnabled(false);
            m_batchAddButton->setEnabled(false);
            
            // 禁用表格编辑
            m_fieldsTable->setEditTriggers(QAbstractItemView::NoEditTriggers);
            
            // 禁用所有组合框
            for (auto combo : m_typeComboBoxes) {
                combo->setEnabled(false);
            }
            
            // 只保留取消按钮，隐藏确定按钮
            m_buttonBox->button(QDialogButtonBox::Ok)->setEnabled(false);
        }
    }
}

void SimulatorConfigDialog::addField()
{
    int row = m_fieldsTable->rowCount();
    m_fieldsTable->insertRow(row);
    
    // 添加默认值
    QTableWidgetItem *nameItem = new QTableWidgetItem(QString("field_%1").arg(row + 1));
    m_fieldsTable->setItem(row, 0, nameItem);
    
    // 创建类型下拉框
    QComboBox *typeCombo = new QComboBox();
    typeCombo->addItem("BOOL");
    typeCombo->addItem("BYTE");
    typeCombo->addItem("INT");
    typeCombo->addItem("DINT");
    typeCombo->addItem("WORD");
    typeCombo->addItem("DWORD");
    typeCombo->addItem("REAL");
    typeCombo->addItem("STRING");
    typeCombo->setCurrentText("INT");

    m_fieldsTable->setCellWidget(row, 1, typeCombo);
    m_typeComboBoxes[row] = typeCombo; // 保存引用以便后续使用

    QTableWidgetItem *valueItem = new QTableWidgetItem("0");
    m_fieldsTable->setItem(row, 2, valueItem);

    // 长度列（仅对STRING类型有效）
    QSpinBox *lengthSpinBox = new QSpinBox();
    lengthSpinBox->setRange(1, 1000);
    lengthSpinBox->setValue(10);
    lengthSpinBox->setEnabled(false); // 默认为INT类型，所以禁用
    m_fieldsTable->setCellWidget(row, 3, lengthSpinBox);

    updatePacketInfo();

    // 连接类型组合框的信号
    connect(typeCombo, &QComboBox::currentTextChanged, this, &SimulatorConfigDialog::updatePacketInfo);
    connect(typeCombo, &QComboBox::currentTextChanged, this, &SimulatorConfigDialog::onFieldTypeChanged);
    connect(lengthSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &SimulatorConfigDialog::updatePacketInfo);
}

void SimulatorConfigDialog::removeField()
{
    QList<QTableWidgetItem*> selectedItems = m_fieldsTable->selectedItems();
    if (!selectedItems.isEmpty()) {
        int row = selectedItems.first()->row();
        
        // 删除对应的组合框引用
        if (m_typeComboBoxes.contains(row)) {
            m_typeComboBoxes.remove(row);
        }
        
        // 更新后续行的索引
        QMap<int, QComboBox*> newMap;
        for (auto it = m_typeComboBoxes.begin(); it != m_typeComboBoxes.end(); ++it) {
            if (it.key() > row) {
                newMap[it.key() - 1] = it.value();
            } else if (it.key() < row) {
                newMap[it.key()] = it.value();
            }
        }
        m_typeComboBoxes = newMap;
        
        m_fieldsTable->removeRow(row);
        updatePacketInfo();
    }
}

void SimulatorConfigDialog::saveSettings()
{
    bool configChanged = false;
    
    if (m_simulator) {
        // 检查是否有配置变化
        if (m_simulator->name() != m_nameEdit->text() ||
            m_simulator->targetAddress() != m_targetAddressEdit->text() ||
            m_simulator->targetPort() != m_targetPortSpinBox->value() ||
            m_simulator->messageInterval() != m_messageIntervalSpinBox->value() ||
            m_simulator->packetId() != m_packetIdSpinBox->value() ||
            m_simulator->isBigEndian() != m_bigEndianCheckBox->isChecked()) {
            configChanged = true;
        }
        
        m_simulator->setName(m_nameEdit->text());
        m_simulator->setTargetAddress(m_targetAddressEdit->text());
        m_simulator->setTargetPort(m_targetPortSpinBox->value());
        m_simulator->setMessageInterval(m_messageIntervalSpinBox->value());
        
        // 保存报文配置
        m_simulator->setPacketId(m_packetIdSpinBox->value());
        m_simulator->setBigEndian(m_bigEndianCheckBox->isChecked());
        
        // 检查字段是否有变化
        QList<PacketField> fields;
        for (int i = 0; i < m_fieldsTable->rowCount(); ++i) {
            QString name = m_fieldsTable->item(i, 0)->text();

            // 获取类型组合框的值
            QComboBox *typeCombo = qobject_cast<QComboBox*>(m_fieldsTable->cellWidget(i, 1));
            QString typeStr = typeCombo ? typeCombo->currentText() : "Int";

            QString valueStr = m_fieldsTable->item(i, 2)->text();

            // 获取长度值（仅对STRING类型有效）
            QSpinBox *lengthSpinBox = qobject_cast<QSpinBox*>(m_fieldsTable->cellWidget(i, 3));
            int stringLength = lengthSpinBox ? lengthSpinBox->value() : 10;

            PacketField field;
            field.setName(name);
            field.setType(PacketField::stringToType(typeStr));
            field.setValue(valueStr);
            field.setStringLength(stringLength);

            fields.append(field);
        }
        
        // 检查字段是否发生变化
        if (!configChanged) {
            QList<PacketField> currentFields = m_simulator->packetFields();
            if (currentFields.size() != fields.size()) {
                configChanged = true;
            } else {
                for (int i = 0; i < currentFields.size(); ++i) {
                    if (currentFields[i].name() != fields[i].name() ||
                        currentFields[i].type() != fields[i].type() ||
                        currentFields[i].value() != fields[i].value() ||
                        currentFields[i].stringLength() != fields[i].stringLength()) {
                        configChanged = true;
                        break;
                    }
                }
            }
        }
        
        m_simulator->setPacketFields(fields);
        
        // 如果配置发生了变化，发出信号
        if (configChanged) {
            emit m_simulator->configurationChanged();
        }
    }
    accept();
}

void SimulatorConfigDialog::updatePacketInfo()
{
    int dataSize = 0;
    for (int i = 0; i < m_fieldsTable->rowCount(); ++i) {
        // 获取类型组合框的值
        QComboBox *typeCombo = qobject_cast<QComboBox*>(m_fieldsTable->cellWidget(i, 1));
        QString typeStr = typeCombo ? typeCombo->currentText() : "Int";
        PacketField::Type type = PacketField::stringToType(typeStr);

        switch (type) {
        case PacketField::BOOL:
        case PacketField::BYTE: dataSize += 1; break;
        case PacketField::INT:
        case PacketField::WORD: dataSize += 2; break;
        case PacketField::DINT:
        case PacketField::DWORD:
        case PacketField::REAL: dataSize += 4; break;
        case PacketField::STRING: {
            QSpinBox *lengthSpinBox = qobject_cast<QSpinBox*>(m_fieldsTable->cellWidget(i, 3));
            int stringLength = lengthSpinBox ? lengthSpinBox->value() : 10;
            dataSize += stringLength;
            break;
        }
        }
    }

    int totalSize = 8 + dataSize; // 8字节报文头 + 数据区大小
    m_packetInfoLabel->setText(QString("报文总长度: %1 字节 (报文头: 8 字节, 数据区: %2 字节)")
                              .arg(totalSize).arg(dataSize));
}

void SimulatorConfigDialog::onFieldTypeChanged()
{
    QComboBox *senderCombo = qobject_cast<QComboBox*>(sender());
    if (!senderCombo) return;

    // 找到发送信号的组合框对应的行
    int row = -1;
    for (auto it = m_typeComboBoxes.begin(); it != m_typeComboBoxes.end(); ++it) {
        if (it.value() == senderCombo) {
            row = it.key();
            break;
        }
    }

    if (row == -1) return;

    QString currentType = senderCombo->currentText();
    QSpinBox *lengthSpinBox = qobject_cast<QSpinBox*>(m_fieldsTable->cellWidget(row, 3));

    if (lengthSpinBox) {
        // 只有STRING类型才启用长度配置
        lengthSpinBox->setEnabled(currentType == "STRING");

        // 如果切换到STRING类型，清空值字段的默认值
        if (currentType == "STRING") {
            QTableWidgetItem *valueItem = m_fieldsTable->item(row, 2);
            if (valueItem && valueItem->text() == "0") {
                valueItem->setText("");
            }
        } else {
            // 如果切换到其他类型，设置默认数值
            QTableWidgetItem *valueItem = m_fieldsTable->item(row, 2);
            if (valueItem && valueItem->text().isEmpty()) {
                valueItem->setText("0");
            }
        }
    }
}
