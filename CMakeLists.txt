cmake_minimum_required(VERSION 3.16)

project(monitor VERSION 0.1 LANGUAGES CXX)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Widgets Svg SvgWidgets)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Widgets Svg SvgWidgets)

# 分类组织源文件
set(CORE_SOURCES
    # 如果你创建了core目录文件，添加在这里
)

set(MODELS_SOURCES
    # 如果你创建了models目录文件，添加在这里
)

set(HOMEPAGE_SOURCES
    homepage/mainwindow.ui
    homepage/mainwindow.cpp
    homepage/mainwindow.h
    homepage/theme_manager.cpp
    homepage/theme_manager.h
    homepage/theme_toggle_button.cpp
    homepage/theme_toggle_button.h
)

set(HP-DISCOVERYPANEL_SOURCES
    hp-discoverypanel/DiscoveredDeviceCard.cpp
    hp-discoverypanel/DiscoveredDeviceCard.h
    hp-discoverypanel/DiscoveryPanel.cpp
    hp-discoverypanel/DiscoveryPanel.h
    hp-discoverypanel/discoverypanel.ui
)

set(HP-DEVICELISTPANEL_SOURCES
    hp-devicelistpanel/devicelistpanel.ui
    hp-devicelistpanel/devicelistpanel.cpp
    hp-devicelistpanel/devicelistpanel.h
    hp-devicelistpanel/DeviceCard.cpp
    hp-devicelistpanel/DeviceCard.h
    hp-devicelistpanel/devicecardmenu.cpp
    hp-devicelistpanel/devicecardmenu.h
)

set(HP-DEVICEDETAILSPANEL_SOURCES
    hp-devicedetailspanel/devicedetailspanel.ui
    hp-devicedetailspanel/DeviceDetailsPanel.cpp
    hp-devicedetailspanel/DeviceDetailsPanel.h
)

set(WORKSTATION_SOURCES
    workstation/workstation.cpp
    workstation/workstation.h
    workstation/workstation.ui
    workstation/configpanel.cpp
    workstation/configpanel.h
    workstation/device_connection_manager.cpp
    workstation/device_connection_manager.h
)

set(WS-MONITORPANEL_SOURCES
    ws-monitorpanel/monitorpanel.cpp
    ws-monitorpanel/monitorpanel.h
    ws-monitorpanel/monitorselector.cpp
    ws-monitorpanel/monitorselector.h
    ws-monitorpanel/addmonitorcard.cpp
    ws-monitorpanel/addmonitorcard.h
    ws-monitorpanel/monitorcard.cpp
    ws-monitorpanel/monitorcard.h
    ws-monitorpanel/monitor_data_client.cpp
    ws-monitorpanel/monitor_data_client.h
    ws-monitorpanel/plotwidget.cpp
    ws-monitorpanel/plotwidget.h
)

set(NETWORK_SOURCES
    network/DiscoveryListener.h
    network/DiscoveryListener.cpp
    network/DiscoveredDevicesManager.h
    network/DiscoveredDevicesManager.cpp
)

set(WS-SIMULATORPANEL_SOURCES
    ws-simulatorpanel/Simulator.cpp
    ws-simulatorpanel/Simulator.h
    ws-simulatorpanel/TCPSimulator.cpp
    ws-simulatorpanel/TCPSimulator.h
    ws-simulatorpanel/UDPSimulator.cpp
    ws-simulatorpanel/UDPSimulator.h
    ws-simulatorpanel/TCPServerSimulator.cpp
    ws-simulatorpanel/TCPServerSimulator.h
    ws-simulatorpanel/PacketField.h
    ws-simulatorpanel/simulatorwidget.cpp
    ws-simulatorpanel/simulatorwidget.h
    ws-simulatorpanel/simulatorcard.cpp
    ws-simulatorpanel/simulatorcard.h
    ws-simulatorpanel/simulatorconfigdialog.cpp
    ws-simulatorpanel/simulatorconfigdialog.h
)

set(UTILS_SOURCES
    utils/monitorutils.cpp
    utils/monitorutils.h
    utils/deviceinfo.h
)

set(PROJECT_SOURCES
    main.cpp
    # 主页
    ${HOMEPAGE_SOURCES}
    ${HP-DISCOVERYPANEL_SOURCES}
    ${HP-DEVICELISTPANEL_SOURCES}
    ${HP-DEVICEDETAILSPANEL_SOURCES}

    ${NETWORK_SOURCES}
    ${UTILS_SOURCES}
    # 工作台
    ${WORKSTATION_SOURCES}
    ${WS-MONITORPANEL_SOURCES}
    ${WS-SIMULATORPANEL_SOURCES}
)


if(${QT_VERSION_MAJOR} GREATER_EQUAL 6)
    find_package(Qt6 REQUIRED COMPONENTS Core Network Svg)

    qt_add_executable(monitor
        MANUAL_FINALIZATION
        ${PROJECT_SOURCES}
    )
# Define target properties for Android with Qt 6 as:
#    set_property(TARGET monitor APPEND PROPERTY QT_ANDROID_PACKAGE_SOURCE_DIR
#                 ${CMAKE_CURRENT_SOURCE_DIR}/android)
# For more information, see https://doc.qt.io/qt-6/qt-add-executable.html#target-creation
else()
    if(ANDROID)
        add_library(monitor SHARED
            ${PROJECT_SOURCES}
        )
# Define properties for Android with Qt 5 after find_package() calls as:
#    set(ANDROID_PACKAGE_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/android")
    else()
        add_executable(monitor
            ${PROJECT_SOURCES}
        )
    endif()
endif()

target_include_directories(monitor PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}  # 包含项目根目录
)

target_link_libraries(monitor PRIVATE Qt${QT_VERSION_MAJOR}::Widgets)
target_link_libraries(monitor PRIVATE Qt6::Core Qt6::Network Qt6::Svg Qt6::SvgWidgets)

# Qt for iOS sets MACOSX_BUNDLE_GUI_IDENTIFIER automatically since Qt 6.1.
# If you are developing for iOS or macOS you should consider setting an
# explicit, fixed bundle identifier manually though.
if(${QT_VERSION} VERSION_LESS 6.1.0)
  set(BUNDLE_ID_OPTION MACOSX_BUNDLE_GUI_IDENTIFIER com.example.monitor)
endif()
set_target_properties(monitor PROPERTIES
    ${BUNDLE_ID_OPTION}
    MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
    MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
    MACOSX_BUNDLE TRUE
    WIN32_EXECUTABLE TRUE
)

include(GNUInstallDirs)
install(TARGETS monitor
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

if(QT_VERSION_MAJOR EQUAL 6)
    qt_finalize_executable(monitor)
endif()

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin)
