#ifndef UDPSIMULATOR_H
#define UDPSIMULATOR_H

#include "Simulator.h"
#include <QUdpSocket>

class UDPSimulator : public Simulator
{
    Q_OBJECT
public:
    explicit UDPSimulator(QObject *parent = nullptr);
    ~UDPSimulator();
    
    void start() override;
    void stop() override;
    Type type() const override;

    QByteArray buildPacket() override;
    
private slots:
    void sendUDPMessage();

private:
    QUdpSocket *m_udpSocket;
};

#endif // UDPSIMULATOR_H
