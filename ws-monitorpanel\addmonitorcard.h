#ifndef ADDMONITORCARD_H
#define ADDM<PERSON><PERSON><PERSON><PERSON>RD_H

#include <QFrame>
#include <QLabel>
#include <QMouseEvent>

class AddMonitorCard : public QFrame
{
    Q_OBJECT

public:
    explicit AddMonitorCard(QWidget *parent = nullptr);

signals:
    void clicked();

private:
    void updateStyle();
    QPixmap drawAddIcon(int size);
    
    QLabel* m_iconLabel;
    QLabel* m_nameLabel;
    
    bool m_hovered;
    bool m_pressed;
    
protected:
    void enterEvent(QEnterEvent *event) override;
    void leaveEvent(QEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
};

#endif // ADDMONITORCARD_H